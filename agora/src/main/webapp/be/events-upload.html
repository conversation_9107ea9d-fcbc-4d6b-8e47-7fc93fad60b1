{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Upload eventi</title>
<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/wysihtml5.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/toolbar.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/parsers.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/locales/bootstrap-wysihtml5.ua-UA.js"></script>
<script src="https://www.siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/events-upload.js?{{ buildNumber }}"></script>

<!-- /theme JS files -->
{% endblock %}

{% block content %}
<a id="eventsUploadStatusUri" style="display: none" href="{{ paths('EVENTS_UPLOAD_SAVE_STATUS') }}" rel="nofollow"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-success">
                    <a href="{{ paths('UPLOAD_EVENTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-danger">
                    <a href="{{ paths('UPLOAD_EVENTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}
        
        <hr>
            
        <div id="upload-event-panel">
            <div class="col-md-12 text-center">
                <h4>Invia il file guida compilato</h4>
                <form id="form-upload-events-verify" method="post" action="{{ paths('EVENTS_UPLOAD_VERIFY') }}">
                    <div class="form-inline">
                        <div class="form-group">
                            <input type="file" name="uploaded-file" class="m-b-md width-full">
                        </div>
                        <button type="submit" class="btn btn-lg btn-primary">Verifica file</button>
                        <!--<button type="submit" class="btn btn-lg btn-remove m-l-sm" {{ uploadId is empty ? 'disabled' : '' }}>{{ label('be.remove') | raw }}</button>-->
                    </div>
                </form>
            </div>
            <div class="col-md-12">
                <hr>
            </div>
            <div class="col-md-12">
                <!-- Progress Bar Section -->
                <div id="upload-progress-container" style="display: none;">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">Progresso Upload Eventi</h5>
                        </div>
                        <div class="panel-body">
                            <div class="progress progress-striped active">
                                <div id="upload-progress-bar" class="progress-bar progress-bar-info" role="progressbar" style="width: 0%">
                                    <span id="upload-progress-text">0%</span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <small><strong>Eventi elaborati:</strong> <span id="upload-processed-count">0</span> / <span id="upload-total-count">0</span></small>
                                </div>
                                <div class="col-md-6 text-right">
                                    <small><strong>Stato:</strong> <span id="upload-current-operation">In attesa...</span></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error Display Section -->
                <div id="upload-errors-container" style="display: none;">
                    <div class="panel panel-danger">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <i class="fa fa-exclamation-triangle"></i> Errori Rilevati
                                <span id="error-count-badge" class="badge badge-danger pull-right">0</span>
                            </h5>
                        </div>
                        <div class="panel-body">
                            <div class="alert alert-warning">
                                <strong>Attenzione:</strong> Sono stati rilevati degli errori durante l'elaborazione degli eventi.
                                Controlla i dettagli nella tabella sottostante e correggi il file Excel prima di riprovare.
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped table-condensed">
                                    <thead>
                                        <tr>
                                            <th style="width: 80px;">Riga</th>
                                            <th style="width: 40%;">Errore</th>
                                            <th>Dati Riga</th>
                                        </tr>
                                    </thead>
                                    <tbody id="upload-errors-table">
                                        <!-- Errors will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <form id="form-upload-events-save" method="post" action="{{ paths('EVENTS_UPLOAD_SAVE') }}?uploadId={{ uploadId }}">
                    <div class="control-group form-group">
                        <div class="controls">
                            {% if uploadErrors %}
                                <button class="btn btn-lg btn-danger btn-block" disabled><span class="btn-subtitle">{{ uploadErrors }}</span></button>
                            {% endif %}
                            <button type="submit" class="btn btn-lg btn-success btn-block" {{ ((uploadCount is empty) or (uploadCount == 0)) ? 'disabled' : '' }}>Importa {{ uploadCount }} eventi. <span class="btn-subtitle">Salvali tutti</span></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

{% endblock %}
