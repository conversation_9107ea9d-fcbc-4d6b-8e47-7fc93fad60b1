{% extends "fe/include/base.html" %}

{% set activePage = 'HOME' %}

{% set metaDescription = label('home.description.meta') | raw %}

{% block title %}{{ label('home.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}
<meta name="robots" content="index, follow">
<meta name="description" content="{{ seoDescription }}">
<link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:url"                content="{{ publicUrl }}" />
<meta property="og:type"               content="website" />
<meta property="og:title"              content="{{ label('home.title.meta') | raw }} | Agorapp" />
<meta property="og:description"        content="{{ metaDescription }}" />
<meta property="og:image"              content="{{ contextPath }}/fe/images/about/cover.png" />
<meta property="og:image:width"        content="1200" />
<meta property="og:image:height"       content="630" />
<meta property="og:image:alt"          content="{{ label('home.title.meta') | raw }} | Agorapp" />
{% endblock %}

{% block pagecss %}
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/tiny-slider/dist/tiny-slider.css">
{% endblock %}

{% block content %}
<!-- some hidden stuff -->
<div id="pagination" style="display: none;">{{ pagination }}</div>
<a id="homeUri" style="display: none" href="{{ paths('HOME') }}" rel="nofollow"></a>
<a id="pageFollowToggleUri" style="display: none" href="{{ paths('PAGE_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>
<a id="eventFollowToggleUri" style="display: none" href="{{ paths('EVENT_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>

<!-- CLIPBOARD TOAST -->
{% include "fe/include/snippets/clipboard-toast.html" %}

{% include "fe/include/snippets/modal-report-event.html" %}

<!-- Hero event START -->
<section class="pt-5 pb-5 position-relative" style="background-image: url({{ contextPath }}/fe/images/home/<USER>">
    <div class="bg-overlay bg-dark opacity-5"></div>
    <!-- Container START -->
    <div class="container">
        <div class="pt-5 pb-5">
            <div class="row position-relative">
                <div class="col-xl-8 col-lg-10 mx-auto pt-sm-5 text-center">
                    <!-- Title -->
                    <h1 class="text-white">{{ label('home.title.events') | raw }}</h1>
                    <p class="text-white">{{ label('home.subtitle.agorapp') | raw }}</p>
                    <div class="mx-auto bg-mode shadow p-4 mt-5">
                        <h5 class="mb-3 text-start">{{ label('home.search.placeholder') | raw }}</h5>
                        <!-- Form START -->
                        <form class="row g-3 justify-content-center">
                            <div class="col-md-12">
                                <!-- What -->
                                <div class="input-group home-search">
                                    <select class="searchall form-control form-control-lg me-1 pe-5"></select>                                      
                                </div>
                            </div>                                            
                        </form>
                        <!-- Form END -->
                    </div>
                </div>                
            </div>
        </div>
    </div>
</section> 
<!-- Hero event END -->
<section class="bg-mode pt-5 pb-0">
    <div class="container">
        <div class="row">
            <div class="col-12 mb-3 text-center">
                <h6>{{ label('home.discover.trends') | raw }}</h6>
                {% set mostUsedTags = cache('tags', 10) %}
                {% if mostUsedTags is not empty %}                
                    <ul class="list-inline mb-0 d-flex flex-wrap gap-2 justify-content-center">                                                                 
                        {% for tag in mostUsedTags %}                            
                            <li class="list-inline-item m-0">
                                <a class="btn btn-outline-light btn-sm" href="{{ paths('RESULTS') }}?q=events&text={{ tag }}">{{ tag }}</a>
                            </li>                                                                          
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Sponsor Events START -->
{% if sponsorEventList is not empty %}
<section class="bg-mode py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 mb-3">
                <div class="d-sm-flex justify-content-between">
                    <!-- Title -->
                    <h4>{{ label('home.sponsor.events') | default('Eventi Sponsorizzati') | raw }}</h4>
                    <a class="btn btn-link" href="{{ paths('EVENTS') }}">{{ label('events.view.all.events') | raw }}</a>
                </div>
            </div>
        </div>
        <div class="row g-4" id="sponsor-masonry-grid">
            {% for entry in sponsorEventList %}
                <div class="col-sm-6 col-xl-4 mb-3" id="sponsoreventfollow-{{ entry.event.id }}">
                    <!-- Sponsor Event item START -->
                    <div class="card h-100 border-lg-lr border-lg-b border-lg-t border-warning">
                        <div class="position-relative">
                            <div class="badge bg-warning text-dark position-absolute top-0 start-0 m-2 z-index-9">
                                <i class="fas fa-star me-1"></i>Sponsorizzato
                            </div>
                            {% if entry.event.coverImageId is not empty %}
                            <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink">
                                <img class="img-event img-fluid" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}" alt="">
                            </a>
                            {% else %}
                            <img class="img-event img-fluid" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="">
                            {% endif %}
                        </div>
                        <!-- Card body START -->
                        <div class="card-body position-relative pt-0">
                            <!-- Tag -->
                            <a class="btn btn-xs btn-primary" href="{{ paths('EVENTS') }}?category={{ entry.event.category }}">{{ decode('eventcategory', entry.event.category) }}</a>
                            <!-- Title -->
                            <h6 class="mt-3"> <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink">{{ entry.event.name }}</a> </h6>
                            <!-- Date and time -->
                            <p class="mb-0 small"> <i class="bi bi-calendar-check pe-1"></i> {{ entry.event.startDate | date('dd MMMM yyyy') }} {% if entry.event.startHour is not empty %} {{ entry.event.startHour }}{% endif %} </p>
                            <!-- Location -->
                            <p class="mb-0 small"> <i class="bi bi-geo-alt pe-1"></i> {{ entry.event.city }} {% if entry.event.provinceCode is not empty %}({{ entry.event.provinceCode }}){% endif %} </p>
                        </div>
                        <!-- Card body END -->
                    </div>
                    <!-- Sponsor Event item END -->
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
<!-- Sponsor Events END -->

<!-- Discover Events START -->
<section class="bg-mode py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 mb-3">
                <div class="d-sm-flex justify-content-between">
                    <!-- Title -->
                    <h4>{{ label('home.discover.events') | raw }}</h4>
                    <a class="btn btn-link" href="{{ paths('EVENTS') }}">{{ label('events.view.all.events') | raw }}</a>
                </div>
            </div>
        </div>
        <div class="row g-4" id="masonry-grid">
            {% if eventList is not empty %}
                {% for entry in eventList %}
                    <div class="col-sm-6 col-xl-4 mb-3" id="eventfollow-{{ entry.event.id }}">
                        <!-- Event item START -->
                        <div class="card h-100 border-lg-lr border-lg-b border-lg-t">
                            <div class="position-relative">
                                {% if entry.event.coverImageId is not empty %}
                                <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink">
                                    <img class="img-event img-fluid" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}" alt="">                                            
                                </a>
                                {% else %}
                                <img class="img-event img-fluid" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="">                                            
                                {% endif %}
                            </div>
                            <!-- Card body START -->
                            <div class="card-body position-relative pt-0">
                                <!-- Tag -->
                                <!--<a class="btn btn-xs btn-primary mt-n3" href="">{{ entry.event.category | capitalize }} </a>-->
                                <h6 class="mt-3"> <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink"> {{ entry.event.name | abbreviate(40) }} </a> </h6>
                                <!-- Date time -->
                                <p class="mb-0 small"> <i class="bi bi-calendar-check pe-1"></i> {{ formatDate(entry.event.startDate, "EEE d MMM yyyy", language) | capitalize }} {{ entry.event.startHour }}</p>
                                <p class="small"> <i class="bi bi-geo-alt pe-1"></i> {{ entry.event.city }} </p>
                                <!-- Avatar group START -->
                                <ul class="avatar-group list-unstyled align-items-center mb-0">
                                    {% if entry.followerPages is not empty %}
                                    {% for followerPage in entry.followerPages %}
                                    <li class="avatar avatar-xs">                                        
                                        <a href="{{ paths('PAGE_BASE') }}/{{ followerPage.identifier }}">
                                            {% if followerPage.profileImageId is not empty %}
                                            <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ followerPage.profileImageId }}">
                                            {% else %}
                                            <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg"> 
                                            {% endif %}
                                        </a>
                                    </li>
                                    {% endfor %}
                                    {% else %}  
                                    <li class="avatar avatar-xs">                                                                                                                    
                                        <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg">                                                                         
                                        </a>
                                    </li>
                                    {% endif %}
                                    {#
                                    <li class="ms-4">
                                        {% if entry.followerCount > 0 and (entry.event.showFollowers == true) %}
                                            {% if entry.followerCount > 1 %}
                                                <small>{{ entry.followerCount }} {{ label('common.people.interested') | raw }}</small>
                                            {% else %}
                                                <small>{{ entry.followerCount }} {{ label('common.person.interested') | raw }}</small>
                                            {% endif %}
                                        {% else %}
                                            <small>{{ label('common.no.people.interested') | raw }}</small>
                                        {% endif%}
                                    </li>
                                    #}
                                </ul>
                                <!-- Avatar group END -->
                                <!-- Button -->
                                <div class="d-flex mt-3 justify-content-between">
                                    <!-- Interested button -->
                                    <div class="w-100">
                                        {% if entry.iFollow %}
                                        <a class="nav-link mb-0 active event-add-follow" data-reload-id="eventfollow-{{ entry.event.id }}" data-value="active" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}" href="#!"> <i class="fa-solid bi-heart-fill me-1"></i> {{ label('common.i.interested') | raw }}</a>
                                        {% else %}
                                        <a class="nav-link mb-0 active event-add-follow" data-reload-id="eventfollow-{{ entry.event.id }}" data-value="inactive" href="#!" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}"> <i class="fa-solid bi-heart me-1"></i> {{ label('common.i.interested') | raw }}</a>
                                        {% endif %}
                                    </div>
                                    <div class="dropdown ms-3">
                                        <a href="#" class="btn btn-sm btn-primary-soft" id="eventActionShare" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-share-fill"></i>
                                        </a>
                                        <!-- Dropdown menu -->
                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="eventActionShare">
                                            <li>
                                                <a class="dropdown-item" href="https://www.facebook.com/sharer/sharer.php?u={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" aria-label="Facebook">
                                                    <i class="bi-facebook"></i> Facebook
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="https://api.whatsapp.com/send?text=Guarda%20questo%20evento%20su%20Agorapp!%20{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" aria-label="Whatsapp">
                                                    <i class="bi-whatsapp"></i> Whatsapp
                                                </a>                            
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="https://t.me/share/url?url={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Telegram">
                                                    <i class="bi-telegram"></i> Telegram
                                                </a>                            
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="https://twitter.com/intent/tweet?url={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Twitter" target="_blank" rel="noopener">
                                                    <i class="bi-twitter-x"></i> X
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="https://www.linkedin.com/sharing/share-offsite/?url={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" aria-label="LinkedIn" target="_blank" rel="noopener">
                                                    <i class="bi-linkedin"></i> LinkedIn
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" data-clipboard-text="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" id="copyToClipboardLink" aria-label="{{ label('common.copy.to.clipboard') | raw }}">
                                                    <i class="bi-clipboard-check"></i> {{ label('common.copy.to.clipboard') | raw }}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <!-- Card body END -->
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
</section>
<!-- Discover Events END -->

<!-- Discover Pages START -->
<section class="bg-mode py-5">
    <div class="container pt-5">
        <div class="row">
            <div class="col-12 mb-3">
                <div class="d-sm-flex justify-content-between">
                    <!-- Title -->
                    <h4>{{ label('home.discover.pages') | raw }}</h4>
                </div>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-12">
                {% if pageList is not empty %}     
                    <div class="card">
                        <!-- Card body START -->
                        <div class="card-body" id="whopagelist">
                            <div class="tiny-slider arrow-hover">
                                <div class="tiny-slider-inner tiny-slider-page-home ms-n4" data-arrow="true" data-dots="false" data-items-xl="4" data-items-lg="3" data-items-md="2" data-items-sm="2" data-items-xs="1" data-gutter="12" data-edge="30">
                                    <!-- Slider items -->
                                    {% for entry in pageList %}
                                    <div>
                                        <!-- Card add friend item START -->
                                        <div class="card shadow-none text-center border border-agora">
                                            <!-- Card body -->
                                            <div class="card-body p-2 pb-0">
                                                <div class="avatar avatar-xl">
                                                    {% if entry.page.profileImageId is not empty %}
                                                    <a href="{{ paths('PAGE_BASE') }}/{{ entry.page.identifier }}"><img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.page.profileImageId }}" alt=""></a>
                                                    {% else %}
                                                    <a href="{{ paths('PAGE_BASE') }}/{{ entry.page.identifier }}"><img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt=""></a>
                                                    {% endif %}
                                                </div>
                                                <!-- Badge con popover -->
                                                <div class="d-flex flex-wrap align-items-center gap-2 my-2 justify-content-center">                                                    
                                                    {% if  entry.page.showFollowers == true %}
                                                    <span class="badge bg-primary text-white"
                                                          role="button"
                                                          tabindex="0"
                                                          data-bs-toggle="popover"
                                                          data-bs-trigger="hover focus"
                                                          data-bs-placement="bottom"
                                                          title="{{ label('badge.page.info.title') | raw }}"
                                                          data-bs-content="{{ label('badge.page.info.content') }} {{ followerCount }} {{ label('common.follower') | raw }}">
                                                        <i class="bi bi-person me-1"></i> {{ followerCount }} {{ label('common.follower') | raw }}
                                                    </span>                                                    
                                                    {% endif %}
                                                </div>
                                                <h6 class="card-title mb-1 mt-3 text-truncate"> <a href="{{ paths('PAGE_BASE') }}/{{ entry.page.identifier }}"> {{ entry.page.name }} </a></h6>
                                                <small>{% autoescape false %}{{ newline( entry.page.description) | abbreviate(150) }}{% endautoescape %}</small>
                                                
                                            </div>
                                            <!-- Card footer -->
                                            <div class="card-footer p-2 border-bottom">
                                                <button class="btn btn-sm btn-outline-primary rounded-pill w-100 page-add-follow" data-tiny="true" data-value="inactive" data-reload-id="whopagelist" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-page-id="{{ entry.page.id }}"> {{ label('common.follow') | raw }} </button>
                                            </div>
                                        </div>
                                        <!-- Card add friend item END -->
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <!-- Card body END -->                
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>
<!-- Discover Pages END -->
      
{% endblock %}       

{% block pagescripts %}     
<script defer src="{{ contextPath }}/fe/js/clipboard.min.js"></script>
<script src="https://unpkg.com/masonry-layout@4/dist/masonry.pkgd.min.js"></script>
<script src="https://unpkg.com/imagesloaded@4/imagesloaded.pkgd.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    var clipboard = new ClipboardJS('#copyToClipboardLink');

    clipboard.on('success', function (e) {
        let myAlert = document.querySelector('.toast');
        let bsAlert = new bootstrap.Toast(myAlert);
        bsAlert.show();

        e.clearSelection();
    });
});
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Initialize masonry for sponsor events
    var sponsorGrid = document.querySelector('#sponsor-masonry-grid');
    var sponsorMsnry = null;

    if (sponsorGrid) {
        sponsorMsnry = new Masonry(sponsorGrid, {
            itemSelector: '.col-sm-6.col-xl-4',
            columnWidth: '.col-sm-6.col-xl-4',
            percentPosition: true,
            gutter: 0
        });

        // Wait for images to load
        imagesLoaded(sponsorGrid, function() {
            sponsorMsnry.layout();
        });
    }

    // Initialize masonry for regular events
    var grid = document.querySelector('#masonry-grid');
    var msnry = null;

    if (grid) {
        msnry = new Masonry(grid, {
            itemSelector: '.col-sm-6.col-xl-4',
            columnWidth: '.col-sm-6.col-xl-4',
            percentPosition: true,
            gutter: 0
        });

        // Wait for images to load
        imagesLoaded(grid, function() {
            msnry.layout();
        });
    }

    // Re-layout when window is resized
    window.addEventListener('resize', function() {
        if (sponsorMsnry) sponsorMsnry.layout();
        if (msnry) msnry.layout();
    });
});
</script>
<script src="{{ contextPath }}/fe/js/pages/home.js?{{ buildNumber }}"></script>
{% endblock %}
