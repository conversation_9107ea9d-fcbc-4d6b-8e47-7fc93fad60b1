{% extends "fe/include/base.html" %}
{% set page = 'PAGE' %}
{% set metaTitle = pageDb.name + ' | Agorapp' %}
{% set metaDescription = pageDb.description | abbreviate(170) %}

{% block title %}{{ metaTitle }}{% endblock %}

{% block canonical %}

<meta name="description" content="{{ metaDescription }}">
<link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:url"                content="{{ publicUrl }}" />
<meta property="og:type"               content="website" />
<meta property="og:title"              content="{{ metaTitle }}" />
<meta property="og:description"        content="{{ metaDescription }}" />
<meta property="og:image"              content="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageDb.coverImageId }}" />
<meta property="og:image:width"        content="1200" />
<meta property="og:image:height"       content="630" />
<meta property="og:image:alt"          content="{{ metaTitle }}" />
{% endblock %}


{% block pagecss %}
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/css/description-truncation.css?{{ buildNumber }}">
{% endblock %}


{% block content %}
<div id="event.requested" style="display: none">{{ label('common.register') | raw }}</div>

<!-- MODAL RICHIEDI EVENTO -->
{% include "fe/include/snippets/modal-request-event.html" %}

<!-- MODAL RICHIEDI EVENTO -->
{% include "fe/include/snippets/modal-report-page.html" %}

<!-- MODAL RICEVI NOTIFICHE -->
{% include "fe/include/snippets/modal-receive-notifications.html" %}

<!-- CLIPBOARD TOAST -->
{% include "fe/include/snippets/clipboard-toast.html" %}

<a id="thisPageUri" style="display: none" href="{{ paths('PAGE_BASE') }}/{{ pageDb.identifier }}" rel="nofollow"></a>
<a id="pageFollowToggleUri" style="display: none" href="{{ paths('PAGE_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>
<a id="pageNotificationToggleUri" style="display: none" href="{{ paths('PAGE_NOTIFICATION_TOGGLE') }}" rel="nofollow"></a>
<a id="pageRemoveRequestUri" style="display: none" href="{{ paths('PAGE_EVENT_REQUEST_REMOVE') }}" rel="nofollow"></a>
<a id="pageReleaseUri" style="display: none" href="{{ paths('PAGE_RELEASE') }}" rel="nofollow"></a>
<a id="mergePageUri" style="display: none" href="{{ paths('PAGE_DIFF') }}"></a>
<a id="dataPagesUri" style="display: none" href="{{ paths('DATA_PAGES') }}?ownerId={{ user.id }}"></a>

<div id="event.request.remove" style="display: none">{{ label('event.request.remove') | raw }}</div>
<div id="common.show.more" style="display: none">{{ label('common.show.more') | raw }}</div>
<div id="common.show.less" style="display: none">{{ label('common.show.less') | raw }}</div>
<div id="page.release.confirm.title" style="display: none">{{ label('page.release.confirm.title') | raw }}</div>
<div id="page.release.confirm.message" style="display: none">{{ label('page.release.confirm.message') | raw }}</div>
<div id="page.release.success" style="display: none">{{ label('page.release.success') | raw }}</div>
<div id="page.release.error" style="display: none">{{ label('page.release.error') | raw }}</div>
<div id="currentPageId" style="display: none">{{ pageDb.id }}</div>

<!-- Container START -->
<div class="container">
    <div class="row g-0">
        <div class="col-lg-8 border-lg-lr">
            <!-- Card START -->
            <div class="card mb-4 border-lg-b">
                {% if pageDb.coverImageId is not empty %}
                <div class="h-200px" style="background-image:url({{ paths('IMAGE_SYSTEM') }}?oid={{ pageDb.coverImageId }}); background-position: center; background-size: cover; background-repeat: no-repeat;"></div>
                {% else %}
                <div class="h-200px" style="background-image:url({{ contextPath }}/fe/images/bg/placeholder-{{ pageDb.pageType | default('person') }}.jpg); background-position: top; background-size: cover; background-repeat: no-repeat;"></div>
                {% endif %}
                <!-- Card body START -->
                <div class="card-body py-0">
                    <div class="d-sm-flex align-items-start text-center text-sm-start">
                        <div>
                            <!-- Avatar -->
                            <div class="avatar avatar-xxl mt-n5 mb-3">
                                {% if pageDb.profileImageId is not empty %}
                                <img class="avatar-img rounded-circle border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageDb.profileImageId }}" alt="{{ pageDb.name }}">
                                {% else %}
                                <img class="avatar-img rounded-circle border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ pageDb.name }}">
                                {% endif %}
                            </div>
                        </div>
                        <div class="ms-sm-4 mt-sm-3">
                            <!-- Info -->
                            <div class="mb-0">
                                
                                <!-- Badge con popover -->
                                <div class="d-flex flex-wrap align-items-center gap-2 mt-0 mb-3 text-center justify-content-center justify-content-sm-start">

                                    <!-- LOCK\UNLOCK -->
                                    {% if pageDb.pageTagging == 'everyone' %}
                                    <span class="badge bg-agora text-white"
                                          role="button"
                                          tabindex="0"
                                          data-bs-toggle="popover"
                                          data-bs-trigger="hover focus"
                                          data-bs-placement="bottom"
                                          title="{{ label('popover.page.open.title') | raw }}"
                                          data-bs-content="{{ label('popover.page.open.content') | raw }}">
                                      <i class="bi bi-unlock"></i>
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary text-white"
                                          role="button"
                                          tabindex="0"
                                          data-bs-toggle="popover"
                                          data-bs-trigger="hover focus"
                                          data-bs-placement="bottom"
                                          title="{{ label('popover.page.closed.title') | raw }}"
                                          data-bs-content="{{ label('popover.page.closed.content') | raw }}">
                                      <i class="bi bi-lock"></i>
                                    </span>
                                    {% endif %}

                                    <!-- INFO POPOVER -->
                                    {% set popoverContent = "" %}

                                    {% if pageDb.claimed == true %}
                                        {% set popoverContent = popoverContent + (label('popover.claimed.page') | raw) %}
                                    {% endif %}
                                    {% if pageDb.isUserPage == true %}
                                        {% set popoverContent = popoverContent + (label('popover.is.user.page') | raw) %}
                                    {% endif %}
                                    {% if pageDb.isUserPage == false and pageDb.claimed != true %}
                                        {% set popoverContent = popoverContent + (label('popover.is.informal.page') | raw) %}
                                    {% endif %}
                                    <span class="badge bg-primary text-white"
                                          role="button"
                                          tabindex="0"
                                          data-bs-toggle="popover"
                                          data-bs-trigger="hover focus"
                                          data-bs-placement="bottom"
                                          data-bs-html="true"
                                          title="{{ label('badge.page.info.title') | raw }}"
                                          data-bs-content="{{ popoverContent }}">
                                        <i class="bi bi-info-circle me-1"></i> {{ label('badge.page.info.title') | raw }}
                                    </span>

                                    <!-- FOLLOWERS -->
                                    {% if pageDb.showFollowers == true %}
                                        <span class="badge bg-secondary text-white"
                                              role="button"
                                              tabindex="0"
                                              data-bs-toggle="popover"
                                              data-bs-trigger="hover focus"
                                              data-bs-placement="bottom"
                                              title="{{ label('badge.page.info.title') | raw }}"
                                              data-bs-content="{{ label('badge.page.info.content') }} {{ followerCount }} {{ label('common.follower') | raw }}">
                                            <i class="bi bi-person me-1"></i> {{ followerCount }} {{ label('common.follower') | raw }}
                                        </span>
                                    {% endif %}
                                </div>
                                
                                <h1 class="h5 mb-3 d-block">{{ pageDb.name }}</h1>
                            </div>

                        </div>
                    </div>
                    <!-- List profile -->

                </div>
                <!-- Card body END -->

            </div>
            <!-- Card END -->

            <div class="card border-lg-b" id="panelFollower">
                <!-- Card body START -->
                <div class="card-body position-relative pt-0">

                    <div class="description-content mb-3"
                         data-auto-truncate="true"
                         data-truncate-length="{{ firm.descriptionTruncateLength | default(300) }}">
                        {% autoescape false %}{{ newline(pageDb.description) }}{% endautoescape %}
                    </div>
                    {% if pageDb.tags is not empty %}
                    <p>
                    <ul class="nav nav-stack gap-0 gap-sm-2">
                        <li class="nav-item">
                            <i class="bi bi-tags-fill"></i>
                            {% for tag in pageDb.tags %}
                            <a href="{{ paths('RESULTS') }}?q=pages&text={{ tag }}">#{{ tag | lower }}</a>
                            {% endfor %}
                        </li>
                    </ul>
                    </p>
                    {% endif %}
                    <div class="d-flex flex-column flex-md-row justify-content-start ms-sm-auto gap-2">
                        <!-- Bottone richiesta evento - tutta la larghezza su mobile -->
                        {% if eventRequest is empty %}
                        <a href="" data-bs-toggle="modal" data-bs-target="#modalRequestEvent" class="btn btn-primary-soft w-100 flex-lg-fill">
                            <i class="bi bi-calendar-check pe-1"></i> {{ label('page.detail.event.request') | raw }} <span class="small">(Beta)</span>
                        </a>
                        {% else %}
                        <button class="btn btn-secondary-soft w-100 flex-lg-fill page-remove-request mb-2 mb-lg-0" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}">
                            <i class="bi bi-bell-fill pe-1"></i> {{ label('page.detail.event.request.remove') | raw }} <span class="small">(Beta)</span>
                        </button>
                        {% endif %}

                        <div class="d-flex gap-2 w-100 flex-md-fill">
                            <!-- Dropdown Follow/Unfollow -->
                            <div class="dropdown flex-fill">
                                <button class="btn btn-primary-soft w-100 mb-0" type="button" id="profileAction2" data-bs-toggle="dropdown" aria-expanded="false">
                                    {% if pageFollow is empty %}
                                    <i class="bi bi-plus-circle-fill pe-1"></i> {{ label('common.follow') | raw }}
                                    {% else %}
                                    <i class="bi bi-dash-circle-fill pe-1"></i> {{ label('common.unfollow') | raw }}
                                    {% endif %}
                                </button>
                                <!-- Card share action dropdown menu -->
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileAction2">
                                    {% if pageFollow is empty and pageNotification is empty %}
                                    <li>
                                        <a class="dropdown-item page-add-follow-and-add-notification" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            {{ label('common.follow.and.notification.receive') | raw }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item page-add-follow" data-reload-id="panelFollower" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            <i class="bi bi-plus-circle-fill pe-1"></i> {{ label('common.follow') | raw }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item page-add-notification" data-reload-id="panelFollower" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            <i class="bi bi-bell-fill pe-1"></i> {{ label('common.notification.receive') | raw }}
                                        </a>
                                    </li>
                                    {% elseif pageFollow is not empty and pageNotification is empty %}
                                    <li>
                                        <a class="dropdown-item page-add-follow" data-reload-id="panelFollower" data-value="active" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            <i class="bi bi-dash-circle-fill pe-1"></i> {{ label('common.unfollow') | raw }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item page-add-notification" data-reload-id="panelFollower" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            <i class="bi bi-bell-fill pe-1"></i> {{ label('common.notification.receive') | raw }}
                                        </a>
                                    </li>
                                    {% elseif pageFollow is empty and pageNotification is not empty %}
                                    <li>
                                        <a class="dropdown-item page-add-follow" data-reload-id="panelFollower" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            <i class="bi bi-plus-circle-fill pe-1"></i> {{ label('common.follow') | raw }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item page-add-notification" data-reload-id="panelFollower" data-value="active" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            <i class="bi bi-bell-fill pe-1"></i> {{ label('common.notification.not.receive') | raw }}
                                        </a>
                                    </li>
                                    {% elseif pageFollow is not empty and pageNotification is not empty %}
                                    <li>
                                        <a class="dropdown-item page-remove-follow-and-remove-notification" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            {{ label('common.unfollow.and.notification.not.receive') | raw }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item page-add-follow" data-reload-id="panelFollower" data-value="active" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            <i class="bi bi-dash-circle-fill pe-1"></i> {{ label('common.unfollow') | raw }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item page-add-notification" data-reload-id="panelFollower" data-value="active" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-page-id="{{ pageDb.id }}" href="#">
                                            <i class="bi bi-bell-fill pe-1"></i> {{ label('common.notification.not.receive') | raw }}
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>

                            <div class="dropdown">
                                <button class="btn btn-light mb-0" type="button" id="profileActionDropdown" data-bs-toggle="dropdown" aria-expanded="false" style="min-width: 42px;">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <!-- Card share action dropdown menu -->
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileActionDropdown">
                                    {% if not pageDb.isUserPage and not (pageDb.ownerId == user.id) %}
                                    <li><a class="dropdown-item" href="{{ paths('PAGE_CLAIM') }}"><i class="bi bi-person-check pe-1"></i> {{ label('page.detail.claim.page') | raw }}</a></li>
                                    {% endif %}
                                    <li>
                                        {% if pageReport is empty %}
                                        <a class="dropdown-item" href="" data-bs-toggle="modal" data-bs-target="#modalReportPage"><i class="bi bi-flag pe-1"></i> {{ label('page.detail.report.page') | raw }}</a>
                                        {% else %}
                                        <button class="dropdown-item" disabled><i class="bi bi-flag-fill pe-1"></i> {{ label('page.detail.reported.page') | raw }}</button>
                                        {% endif %}
                                    </li>
                                    {% if pageDb.ownerId == user.id or user.profileType == 'system' or user.profileType == 'admin' %}
                                    <li><a class="dropdown-item" href="{{ paths('PAGE_EDIT') }}?oid={{ pageDb.id }}&backUrl={{ paths('PAGE_BASE') }}/"><i class="bi bi-pencil pe-1"></i> {{ label('page.edit') | raw }}</a></li>
                                    {% endif %}
                                    {% if pageDb.ownerId == user.id and pageDb.isUserPage != true %}
                                    <li><button class="dropdown-item page-release-btn" data-page-id="{{ pageDb.id }}"><i class="bi bi-box-arrow-right pe-1"></i> {{ label('page.release.button') | raw }}</button></li>
                                    {% endif %}
                                    {% if pageDb.ownerId == user.id or user.profileType == 'system' or user.profileType == 'admin' %}
                                    {% if user.profileType == 'system' or user.profileType == 'admin' %}
                                    <li><button class="dropdown-item" data-page-id="{{ pageDb.id }}" data-bs-toggle="modal" data-bs-target="#modalPageMerge"><i class="bi bi-arrows-collapse pe-1"></i> {{ label('page.merge') | raw }}</button></li>
                                    {% endif %}
                                    {% endif %}

                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card body END -->
            </div>

            <!-- Events START -->
            <div class="card">
                <!-- Card header START -->
                <div class="card-header d-flex align-items-center justify-content-between border-0 pb-0">
                    <h5 class="card-title mb-sm-0">Agenda</h5>
                    {% if user is not empty %}
                    {% if pageDb.pageTagging == 'everyone' or pageDb.ownerId == user.id %}
                    <div class="d-flex">
                        <div class="dropdown">
                            <button class="btn btn-primary mt-2 mt-sm-0 w-100 w-md-auto dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fa-solid fa-plus pe-1"></i> {{ label('common.create.event') | raw }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item d-flex align-items-center" href="{{ paths('EVENT_ADD') }}?pageId={{ pageDb.id }}&backUrl={{ paths('PAGE_BASE') }}/{{ pageDb.identifier }}">
                                        <svg id='Calendar_Date_One_32' width='32' height='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='32' height='32' stroke='none' fill='#000000' opacity='0'/>


                                            <g transform="matrix(2 0 0 2 16 16)" >
                                                <g style="" >
                                                    <g transform="matrix(1 0 0 1 -2.37 -5.11)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.75, -2.02)" d="M 4.75342 0.75 L 4.75342 3.28918" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 2.11 -5.11)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.24, -2.02)" d="M 9.23975 0.75 L 9.23975 3.28918" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 -0.13 0.42)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -7.55)" d="M 1.09183 10.5249 C 1.25317 11.9642 2.42311 13.1071 3.86983 13.1768 C 4.86978 13.2249 5.89127 13.2499 6.99991 13.2499 C 8.10855 13.2499 9.13003 13.2249 10.13 13.1768 C 11.5767 13.1071 12.7466 11.9642 12.908 10.5249 C 13.0166 9.55554 13.1058 8.56176 13.1058 7.54941 C 13.1058 6.53707 13.0166 5.54329 12.908 4.57397 C 12.7466 3.13459 11.5767 1.99174 10.13 1.92207 C 9.13003 1.87391 8.10855 1.84888 6.99991 1.84888 C 5.89127 1.84888 4.86978 1.87391 3.86983 1.92207 C 2.42311 1.99174 1.25317 3.13459 1.09183 4.57397 C 0.983182 5.54329 0.894043 6.53707 0.894043 7.54941 C 0.894043 8.56176 0.983182 9.55554 1.09183 10.5249 Z" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 -0.13 0.42)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -7.55)" d="M 7 5.29803 L 7 9.80076" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 -0.88 -1.26)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.25, -5.86)" d="M 5.49909 6.42372 L 5.87432 6.42372 C 6.49601 6.42372 7 5.91973 7 5.29803 L 7 5.29803" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 -0.12 2.68)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -9.8)" d="M 8.50098 9.80078 L 5.49915 9.80078" stroke-linecap="round" />
                                                    </g>
                                                </g>
                                            </g>
                                        </svg>
                                        <div class="ms-2">
                                            <div class="fw-bold">{{ label('common.create.event') | raw }}</div>
                                            <small class="text-gray-600">{{ label('event.single.description') | raw }}</small>
                                        </div>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item d-flex align-items-center" href="{{ paths('EVENT_ADD') }}?isContainer=true&pageId={{ pageDb.id }}&backUrl={{ paths('PAGE_BASE') }}/{{ pageDb.identifier }}">
                                        <svg id='Calendar_Mark_32' width='32' height='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='32' height='32' stroke='none' fill='#000000' opacity='0'/>


                                            <g transform="matrix(2 0 0 2 16 16)" >
                                                <g style="" >
                                                    <g transform="matrix(1 0 0 1 -2.37 -5.11)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.75, -2.02)" d="M 4.75342 0.75 L 4.75342 3.28918" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 2.11 -5.11)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.24, -2.02)" d="M 9.23975 0.75 L 9.23975 3.28918" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 -0.13 0.42)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -7.55)" d="M 1.09183 10.5249 C 1.25317 11.9642 2.42311 13.1071 3.86983 13.1768 C 4.86978 13.2249 5.89127 13.2499 6.99991 13.2499 C 8.10855 13.2499 9.13003 13.2249 10.13 13.1768 C 11.5767 13.1071 12.7466 11.9642 12.908 10.5249 C 13.0166 9.55554 13.1058 8.56176 13.1058 7.54941 C 13.1058 6.53707 13.0166 5.54329 12.908 4.57397 C 12.7466 3.13459 11.5767 1.99174 10.13 1.92207 C 9.13003 1.87391 8.10855 1.84888 6.99991 1.84888 C 5.89127 1.84888 4.86978 1.87391 3.86983 1.92207 C 2.42311 1.99174 1.25317 3.13459 1.09183 4.57397 C 0.983182 5.54329 0.894043 6.53707 0.894043 7.54941 C 0.894043 8.56176 0.983182 9.55554 1.09183 10.5249 Z" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 -2.88 -1.08)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.25, -6.05)" d="M 4 6.04944 L 4.5 6.04944" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 -2.88 1.92)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.25, -9.05)" d="M 4 9.04944 L 4.5 9.04944" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 -0.13 -1.08)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -6.05)" d="M 6.75 6.04944 L 7.25 6.04944" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 2.63 -1.08)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.75, -6.05)" d="M 9.5 6.04944 L 10 6.04944" stroke-linecap="round" />
                                                    </g>
                                                    <g transform="matrix(1 0 0 1 -0.13 1.92)" >
                                                        <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -9.05)" d="M 6.75 9.04944 L 7.25 9.04944" stroke-linecap="round" />
                                                    </g>
                                                </g>
                                            </g>
                                        </svg>
                                        <div class="ms-2">
                                            <div class="fw-bold">{{ label('event.create.serial') | raw }}</div>
                                            <small class="text-gray-600">{{ label('event.serial.description') | raw }}</small>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    {% endif %}
                    {% else %}
                    <a class="btn btn-primary" href="{{ paths('REGISTER') }}"> <i class="fa-solid fa-plus pe-1"></i> {{ label('page.detail.register.and.create.event') | raw }}</a>
                    {% endif %}
                </div>
                <!-- Card header END -->
                {% if eventEntryList is not empty %}
                <!-- Card body START -->
                <div class="card-body pt-0">
                    <hr>
                    <div class="containerEvents">
                        <div class="oneEvent">
                            {% for eventEntry in eventEntryList %}
                            {% set show = true %}
                            {% if eventEntry.event.type == 'container' %}
                            {% set show = eventEntry.event.childIds is not empty %}
                            {% endif %}
                            {% if show %}
                            <div class="row">
                                {% set pastEvent = false %}
                                {% if daysbetween(eventEntry.event.startDate, today()) > 0 %}
                                    {% if eventEntry.event.endDate is not empty %}
                                        {% if daysbetween(eventEntry.event.endDate, today()) > 0 %}
                                            {% set pastEvent = true %}
                                        {% endif %}
                                    {% else %}
                                        {% set pastEvent = true %}
                                    {% endif %}
                                {% endif %}
                                <div class="d-sm-flex align-items-center {{ pastEvent ? 'opacity-50':'' }}">
                                    <!-- Avatar -->
                                    <div class="{{ eventEntry.event.type == 'container' ? 'avatar avatar-xxl' : 'avatar avatar-xl' }}">
                                        {% if eventEntry.event.type == 'container' %}
                                        <a href="{{ paths('EVENT_BASE') }}/{{ eventEntry.event.identifier }}?pageId={{ pageDb.id }}" class="eventlink">
                                        {% else %}
                                        <a href="{{ paths('EVENT_BASE') }}/{{ eventEntry.event.identifier }}" class="eventlink">
                                        {% endif %}
                                            {% if eventEntry.event.coverImageId is not empty %}
                                            <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ eventEntry.event.coverImageId }}" alt="{{ eventEntry.event.name }}">
                                            {% else %}
                                            {% if pageDb.coverImageId is not empty %}
                                            <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageDb.coverImageId }}" alt="{{ eventEntry.event.name }}">
                                            {% else %}
                                            <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ eventEntry.event.name }}">
                                            {% endif %}
                                            {% endif%}
                                        </a>
                                    </div>
                                    <div class="ms-sm-4 mt-2 mt-sm-0">
                                        <!-- Info -->
                                        <h5 class="mb-1">
                                            {% if eventEntry.event.type == 'container' %}
                                            <a href="{{ paths('EVENT_BASE') }}/{{ eventEntry.event.identifier }}?pageId={{ pageDb.id }}"> {{ eventEntry.event.name }} </a>
                                            {% else %}
                                            <a href="{{ paths('EVENT_BASE') }}/{{ eventEntry.event.identifier }}"> {{ eventEntry.event.name }} </a>
                                            {% endif %}
                                        </h5>
                                        {% if eventEntry.event.type == 'container' %}
                                        <span class="badge bg-primary mb-4">Container</span>
                                        <ul class="nav nav-stack small">
                                            <li class="nav-item">
                                                <i class="bi bi-calendar-check pe-1"></i>{{ formatDate(eventEntry.event.startDate, "EEE d MMM yyyy", language) }} {{ eventEntry.event.startHour }} - {{ formatDate(eventEntry.event.endDate, "EEE d MMM yyyy", language) }} {{ eventEntry.event.endHour }}
                                            </li>
                                            <li class="nav-item">
                                                <i class="bi bi-people pe-1"></i> {{ eventEntry.event.pageIds | length }} {{ label('common.pages') | raw }}
                                            </li>
                                            <li class="nav-item">
                                                <i class="bi bi-calendar-check pe-1"></i> {{ eventEntry.event.childIds | length }} {{ label('common.events') | raw }}
                                            </li>
                                        </ul>
                                        {% else %}
                                        <ul class="nav nav-stack small">
                                            <li class="nav-item">
                                                <i class="bi bi-calendar-check pe-1"></i>{{ formatDate(eventEntry.event.startDate, "EEE d MMM yyyy", language) }} {{ eventEntry.event.startHour }}
                                            </li>
                                            <li class="nav-item">
                                                <i class="bi bi-geo-alt pe-1"></i> {{ eventEntry.event.city }}
                                            </li>
                                            {% if eventEntry.followerCount > 0 and (eventEntry.event.showFollowers == true) %}
                                            <li class="nav-item">
                                                <i class="bi bi-people pe-1"></i> {{ eventEntry.followerCount }} {{ label('common.participants') | raw }}
                                            </li>
                                            {% endif %}
                                        </ul>
                                        {% endif %}
                                    </div>
                                    {#
                                    <!-- Button -->
                                    <div class="d-flex mt-3 ms-auto">
                                        <div class="dropdown">
                                            <!-- Card share action menu -->
                                            <button class="icon-md btn btn-secondary-soft" type="button" id="profileAction" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="bi bi-three-dots"></i>
                                            </button>
                                            <!-- Card share action dropdown menu -->
                                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileAction">
                                                <li><a class="dropdown-item" href="#"> <i class="bi bi-bookmark fa-fw pe-2"></i>Share profile in a message</a></li>
                                                <li><a class="dropdown-item" href="#"> <i class="bi bi-file-earmark-pdf fa-fw pe-2"></i>Save your profile to PDF</a></li>
                                                <li><a class="dropdown-item" href="#"> <i class="bi bi-lock fa-fw pe-2"></i>Lock profile</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#"> <i class="bi bi-gear fa-fw pe-2"></i>Profile settings</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    #}
                                </div>
                            </div>
                            <!-- Events list END -->
                            <hr>
                            {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="row m-t-sm">
                    <div class="col-xs-12 text-center">
                        <div id="loading" class="spinner">
                            <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                        </div>
                    </div>
                </div>
                <div class="row m-t-sm">
                    <div class="pager">
                        {% if resultUrl contains '?' %}
                        {% if skip > 12 %}
                        <a href="{{ resultUrl }}&skip={{ skip - limit }}&limit={{ limit }}" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                        {% if loadmore %}
                        <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                        {% endif %}
                        {% else %}
                        {% if skip == 12 %}
                        <a href="{{ resultUrl }}" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                        {% endif %}
                        {% if loadmore %}
                        <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                        {% endif %}
                        {% endif %}
                        {% else %}
                        {% if skip > 12 %}
                        <a href="{{ resultUrl }}?skip={{ skip - limit }}&limit={{ limit }}" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                        {% if loadmore %}
                        <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                        {% endif %}
                        {% else %}
                        {% if skip == 12 %}
                        <a href="{{ resultUrl }}" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                        {% endif %}
                        {% if loadmore %}
                        <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                        {% endif %}
                        {% endif %}
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <!-- Card body START -->
                <div class="card-body">
                    <div class="alert alert-primary w-100 text-center" role="alert">
                        {{ label('page.detail.no.events') | raw }}
                    </div>
                </div>
                {% endif %}
                <!-- Events list END -->
            </div>
            <!-- Events START -->
        </div>
        <div class="col-lg-4 border-lg-r">
            {% if pageDb.fulladdress is not empty %}
            <!-- Map START -->
            <div class="card mb-4">
                <div class="card-header border-0 pb-0">
                    <h5 class="card-title">{{ label('common.maps') | raw }}</h5>
                    <!-- Button modal -->
                </div>
                <div class="card-body position-relative pt-0">
                    <iframe class="w-100 d-block rounded" height="258" src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&q={{ pageDb.fulladdress }}&zoom=12"  style="border:0;" aria-hidden="false" tabindex="0"></iframe>
                </div>
            </div>
            <!-- Map END -->
            {% endif %}
            <style>
                #map {
                    height: 500px;
                    width: 100%;
                }
            </style>
            <div class="card mb-4">
                <div class="card-header border-0 pb-0">

                    <h5>{{ label('common.maps.event') | raw }}</h2>
                        <div id="map"></div>

                        <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU"></script>
                        <script>
const events = {{ futureEventListJson | raw }};
function initMap() {
const mapCenter = { lat: 41.9028, lng: 12.4964 }; // Centro Italia
const map = new google.maps.Map(document.getElementById("map"), {
zoom: 5,
        center: mapCenter
        });
// Aggiungi un marker per ogni evento
events.forEach(event => {
const lat = parseFloat(event.lat);
const lng = parseFloat(event.lng);
if (!(isNaN(lat) || isNaN(lng))) {
const marker = new google.maps.Marker({
position: { lat: lat, lng: lng },
        map: map,
        title: event.name
        });
const infoWindow = new google.maps.InfoWindow({
content: `<div><strong>${event.name}</strong><br>
                                                <a href="{{ paths('EVENT_BASE') }}/` + event.identifier + `" target="_blank">Maggiore dettagli</a></div>`
        });
// Mostra l'info window quando si clicca sul marker
marker.addListener("click", () => {
infoWindow.open(map, marker);
});
}
});
}

// Inizializza la mappa
initMap();
                        </script>
                </div>
            </div>
            {% if pageDb.websiteUrl is not empty or pageDb.facebookUrl is not empty or pageDb.twitterUrl is not empty or pageDb.instagramUrl is not empty or pageDb.linkedinUrl is not empty or pageDb.youtubeUrl is not empty or pageDb.redditUrl is not empty or pageDb.mediumUrl is not empty or pageDb.tiktok is not empty or pageDb.spotifiyUrl is not empty %}
            <div class="card">
                <div class="card-header border-0 pb-0">
                    <h5 class="card-title">Links</h5>
                    <!-- Button modal -->
                </div>
                <!-- Card body START -->
                <div class="card-body position-relative pt-0">
                    <!-- Date time -->
                    <ul class="list-unstyled mt-3 mb-0">
                        {% if pageDb.websiteUrl is not empty %}
                        <li class="mb-2"> <i class="bi bi-calendar-date fa-fw pe-1"></i> <a href="{{ pageDb.websiteUrl }}">{{ label('common.website') | raw }}</a></li>
                        {% endif %}
                        {% if pageDb.facebookUrl is not empty %}
                        <li class="mb-2"> <i class="bi bi-facebook fa-fw pe-1"></i> <a href="{{ pageDb.facebookUrl }}">Facebook</a></li>
                        {% endif %}
                        {% if pageDb.twitterUrl is not empty %}
                        <li class="mb-2"> <i class="bi bi-twitter fa-fw pe-1"></i> <a href="{{ pageDb.twitterUrl }}">Twitter</a></li>
                        {% endif %}
                        {% if pageDb.instagramUrl is not empty %}
                        <li class="mb-2"> <i class="bi bi-instagram fa-fw pe-1"></i> <a href="{{ pageDb.instagramUrl }}">Instagram</a></li>
                        {% endif %}
                        {% if pageDb.linkedinUrl is not empty %}
                        <li class="mb-2"> <i class="bi bi-linkedin fa-fw pe-1"></i> <a href="{{ pageDb.linkedinUrl }}">Linkedin</a></li>
                        {% endif %}
                        {% if pageDb.youtubeUrl is not empty %}
                        <li class="mb-2"> <i class="bi bi-youtube fa-fw pe-1"></i> <a href="{{ pageDb.youtubeUrl }}">Youtube</a></li>
                        {% endif %}
                        {% if pageDb.redditUrl is not empty %}
                        <li class="mb-2"> <i class="bi bi-reddit fa-fw pe-1"></i> <a href="{{ pageDb.redditUrl }}">Reddit</a></li>
                        {% endif %}
                        {% if pageDb.mediumUrl is not empty %}
                        <li class="mb-2"> <i class="bi bi-medium fa-fw pe-1"></i> <a href="{{ pageDb.mediumUrl }}">Medium</a></li>
                        {% endif %}
                        {% if pageDb.tiktok is not empty %}
                        <li class="mb-2"> <i class="bi bi-tiktok fa-fw pe-1"></i> <a href="{{ pageDb.tiktok }}">TikTok</a></li>
                        {% endif %}
                        {% if pageDb.spotifiyUrl is not empty %}
                        <li class="mb-2"> <i class="bi bi-spotify fa-fw pe-1"></i> <a href="{{ pageDb.spotifiyUrl }}">Spotify</a></li>
                        {% endif %}

                    </ul>
                </div>
                <!-- Card body END -->
            </div   >
            {% endif %}

            {#
            {% set whoPeopleList = follow('whopeople', customerEntry.customer.userId, customerEntry.customer.provinceCode, 5, 'creationDesc') %}
            #}
            {% set whoPeopleList = follow('whopage', customerEntry.customer.userId, customerEntry.customer.provinceCode, 5, 'random', pageDb.id) %}

            {% if whoPeopleList is not empty %}
            <div class="card">
                <!-- Card header START -->
                <div class="card-header pb-0 border-0">
                    <h5 class="card-title mb-0">{{ label('common.who.follow') | raw }}</h5>
                </div>
                <!-- Card header END -->
                <!-- Card body START -->
                <div class="card-body" id="whopeoplelist">
                    {% for whopeople in whoPeopleList %}
                    <!-- Connection item START -->
                    <div class="hstack gap-2 mb-3">
                        <!-- Avatar -->
                        <div class="avatar">
                            <a href="{{ paths('PAGE_BASE') }}/{{ whopeople.page.identifier }}">
                                {% if whopeople.page.profileImageId is not empty %}
                                <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ whopeople.page.profileImageId }}" alt="{{ whopeople.page.name }}">
                                {% else %}
                                <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ whopeople.page.name }}">
                                {% endif %}
                            </a>
                        </div>
                        <!-- Title -->
                        <div class="overflow-hidden">
                            <a class="h6 mb-0" href="{{ paths('PAGE_BASE') }}/{{ whopeople.page.identifier }}">{{ whopeople.page.name }} </a>
                            <p class="mb-0 small text-truncate">{{ decode('area', whopeople.page.pageType | default('person') )}}</p>
                        </div>
                        <!-- Button -->
                        <a class="btn btn-outline-primary rounded-circle icon-md ms-auto page-add-follow" data-value="inactive" data-reload-id="whopeoplelist" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-page-id="{{ whopeople.page.id }}" href="#"><i class="fa-solid fa-plus"> </i></a>
                    </div>
                    <!-- Connection item END -->
                    {% endfor %}
                </div>
                <!-- Card body END -->
            </div>
            {% else %}
            <!--{{ label('page.detail.no.one.else.to.follow') | raw }}-->
            {% endif %}

        </div>
    </div>
    {#
    <div class="row g-0">
        {% if not pageDb.isUserPage and not (pageDb.ownerId == user.id) %}
        <div class="col-lg-6 text-center">
            {{ label('page.detail.manage.page.question') | raw }} <a href="{{ paths('PAGE_CLAIM') }}">{{ label('page.detail.claim.page') | raw }}</a>
        </div>
        {% endif %}
        <div class="col-lg-2 text-center">
            {% if pageReport is empty %}
            <a href="" data-bs-toggle="modal" data-bs-target="#modalReportPage"><i class="bi bi-bandaid pe-1"></i> {{ label('page.detail.report.page') | raw }}</a>
            {% else %}
            <button href="" disabled><i class="bi bi-bandaid-fill pe-1"></i> {{ label('page.detail.reported.page') | raw }}</button>
            {% endif %}
        </div>

    </div> <!-- Row END -->
    #}
</div>
<!-- Container END -->
<div class="d-lg-none">
    {% include "fe/include/snippets/sidenav-left.html" %}
</div>
{% include "fe/include/snippets/modal-page-merge.html" %}
{% endblock %}

{% block pagescripts %}
<script src="{{ contextPath }}/fe/js/content-truncation.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/fe/js/pages/page-detail.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/l10n/it.js"></script>
<script src="{{ contextPath }}/fe/js/description-toggle.js?{{ buildNumber }}"></script>
{% endblock %}