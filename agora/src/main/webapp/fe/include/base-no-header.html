<!DOCTYPE html>
<html lang="it" prefix="og: http://ogp.me/ns#" class="h-100">

    <head>        
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">

        <!-- FAVICON -->             
        <link rel="apple-touch-icon" sizes="180x180" href="{{ contextPath }}/fe/images/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="{{ contextPath }}/fe/images/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="{{ contextPath }}/fe/images/favicon-16x16.png">
        <link rel="manifest" href="{{ contextPath }}/fe/images/site.webmanifest">
        <link rel="mask-icon" href="{{ contextPath }}/fe/images/safari-pinned-tab.svg" color="#8b734d">
        <meta name="msapplication-TileColor" content="#8b734d">
        <meta name="theme-color" content="#8b734d">
        
        <!-- TITLE -->
        <title>{% block title %}{% endblock %}</title>

        <!-- PRECONNECT -->
        <link rel="preconnect" href="https://cdn.iubenda.com">
        <link rel="preconnect" href="https://www.iubenda.com">
        <link rel="preconnect" href="https://hits-i.iubenda.com">
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Kumbh+Sans:wght@100..900&display=swap" rel="stylesheet">
        <link rel="preload" href="{{ contextPath }}/fe/vendor/font-awesome/css/all.min.css" as="style">
        <link rel="preload" href="{{ contextPath }}/fe/vendor/bootstrap-icons/bootstrap-icons.css" as="style">
        <link rel="preload" href="{{ contextPath }}/fe/css/style.css" as="style">
        <link rel="preload" href="{{ contextPath }}/fe/css/custom.css?{{ buildNumber }}" as="style">

        {% if not isLocal %}
            
        {% endif %}
        
        {% if activePage is empty %}
            
        {% endif %}

        <!-- URL CANONICAL -->
        {% block canonical %}{% endblock %}

        <!-- SOCIAL CARDS -->
        {% block socialcards %}{% endblock %}
        
        <!-- MAIN CSS -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap">
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/OverlayScrollbars-master/css/OverlayScrollbars.min.css">
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/font-awesome/css/all.min.css">
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/bootstrap-icons/bootstrap-icons.css">
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/select2/select2.min.css">

        <!-- PAGE CSS -->
        {% block pagecss %}{% endblock %}

        <!-- THEME CSS -->
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/css/style.css">
        <link rel="stylesheet" href="{{ contextPath }}/fe/css/custom.css?{{ buildNumber }}">

        <!-- EXTRA HEAD -->
        {% block pagehead %}{% endblock %}                        

    </head>

    <body>

        <!-- GTM -->
        {% if not isLocal %}

        {% endif %}        

        <!-- CONTENT -->
        <main>
            {% block content %}{% endblock %}
        </main>        
        
        <!-- THEME SCRIPTS -->        
        <script src="{{ contextPath }}/fe/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="{{ contextPath }}/fe/js/jquery.min.js"></script>
        <script src="{{ contextPath }}/fe/js/pages/label.js?{{ buildNumber }}"></script>
        <div id="common.search.page" style="display: none">{{ label('common.search.page') | raw }}</div>
        <div id="common.register" style="display: none">{{ label('common.register') | raw }}</div>
        <div id="common.tryagain" style="display: none">{{ label('common.tryagain') | raw }}</div>
        <div id="common.recover" style="display: none">{{ label('common.recover') | raw }}</div>
        <div id="common.must.registered.event" style="display: none">{{ label('common.must.registered.event') | raw }}</div>
        <div id="common.need.help" style="display: none">{{ label('common.need.help') | raw }}</div>
        <div id="common.contact.assistance" style="display: none">{{ label('common.contact.assistance') | raw }}</div>
        <div id="common.event.added" style="display: none">{{ label('common.event.added') | raw }}</div>
        <div id="common.event.removed" style="display: none">{{ label('common.event.removed') | raw }}</div>
        <div id="common.add.failed" style="display: none">{{ label('common.add.failed') | raw }}</div>
        <div id="common.remove.failed" style="display: none">{{ label('common.remove.failed') | raw }}</div>
        <div id="common.email.sent" style="display: none">{{ label('common.email.sent') | raw }}</div>
        <div id="common.check.inbox" style="display: none">{{ label('common.check.inbox') | raw }}</div>
        <div id="common.mail.not.sent" style="display: none">{{ label('common.mail.not.sent') | raw }}</div>
        <div id="common.ops.error" style="display: none">{{ label('common.ops.error') | raw }}</div>
        <div id="common.delete.failed" style="display: none">{{ label('common.delete.failed') | raw }}</div>
        <div id="common.insertion.failed" style="display: none">{{ label('common.insertion.failed') | raw }}</div>
        <div id="common.continue" style="display: none">{{ label('common.continue') | raw }}</div>
        <div id="common.event.created" style="display: none">{{ label('common.event.created') | raw }}</div>
        <div id="common.page.follow.added" style="display: none">{{ label('common.page.added') | raw }}</div>
        <div id="common.page.follow.removed" style="display: none">{{ label('common.page.follow.removed') | raw }}</div>
        <div id="common.page.notify.added" style="display: none">{{ label('common.page.notify.added') | raw }}</div>
        <div id="common.page.notify.removed" style="display: none">{{ label('common.page.notify.removed') | raw }}</div>
        <div id="common.data.saved.correctly" style="display: none">{{ label('common.data.saved.correctly') | raw }}</div>
        <div id="common.operation.error" style="display: none">{{ label('common.operation.error') | raw }}</div>
        <div id="common.information.saved.success" style="display: none">{{ label('common.information.saved.success') | raw }}</div>
        <div id="password.enter" style="display: none">{{ label('password.enter') | raw }}</div>
        <div id="password.too.easy" style="display: none">{{ label('password.too.easy') | raw }}</div>
        <div id="password.pretty.simple" style="display: none">{{ label('password.pretty.simple') | raw }}</div>
        <div id="password.much.better" style="display: none">{{ label('password.much.better') | raw }}</div>
        <div id="password.very.strong" style="display: none">{{ label('password.very.strong') | raw }}</div> 
        <div id="common.search" style="display: none">{{ label('common.search') | raw }}</div>
        <div id="email.sent.success" style="display: none">{{ label('email.sent.success') | raw }}</div>
        <div id="email.credentials.sent" style="display: none">{{ label('email.credentials.sent') | raw }}</div>
        <div id="common.go.to.login" style="display: none">{{ label('common.go.to.login') | raw }}</div>
        <div id="common.data.save.failed" style="display: none">{{ label('common.data.save.failed') | raw }}</div>
    
        <script src="{{ contextPath }}/fe/vendor/pswmeter/pswmeter.min.js"></script>
        <script src="{{ contextPath }}/fe/js/functions.js"></script>
        <script defer type="text/javascript" src="{{ contextPath }}/fe/js/jquery.validate.min.js"></script>
        {% if (language is empty) or (language == 'it') %}
            <script defer src="{{ contextPath }}/fe/js/localization/messages_it.min.js"> </script>
        {% endif %}
        <script src="https://siteria.it/libs/jquery-blockui/2.70.0/jquery.blockUI.min.js"></script>
        <script defer src="https://siteria.it/libs/uri/1.18.4/URI.js"></script>           
        <script defer src="{{ contextPath }}/fe/js/sweetalert.js"></script>
        
        <script>            
            (function() {                         
                
                // INITIALIZATION OF BLOCKUI
                // =======================================================
                $.blockUI.defaults = {
                    message:  '<div class="fancybox-loading">',
                    css: {
                        padding:        0,
                        margin:         0,            
                        top:            '45%',
                        left:           '50%',
                        right:          '50%',            
                        border:         'none',
                        backgroundColor:'transparent',
                        cursor:         'wait'
                    },
                    overlayCSS:  {
                        backgroundColor: '#000',
                        opacity:         0.4,
                        cursor:          'wait'
                    },
                    baseZ: 1100,
                    showOverlay: true
                }; 
                
            })();
        </script>
        
        <!-- PAGE SCRIPTS -->
        {% block pagescripts %}{% endblock %}

        <!-- LINKING DATA -->
        {% block schema %}{% endblock %}
        
        <script type="application/ld+json">
            {
                "@context": "http://schema.org",
                "@type": "Organization",
                "name": "Agorapp",
                "url": "https://www.agor.app",
                "logo": "{{ contextPath }}/fe/images/logo.svg",
                "sameAs": [
                    "",
                    "",
                    ""
                ]
            }
        </script>    
    </body>

</html>
