<!-- Navbar START-->
<nav class="navbar navbar-expand-lg mx-0 sticky-top" style="top:50px"> 
    <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasSideNavbar">
        <!-- Offcanvas header -->
        <div class="offcanvas-header">
            <button type="button" class="btn-close text-reset ms-auto" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>

        <!-- Offcanvas body -->
        <div class="offcanvas-body d-block px-2 px-lg-0">
            <!-- Card START -->
            <div class="card overflow-hidden">
                <!-- Cover image -->
                {% if customerEntry.page is not empty and customerEntry.page.coverImageId is not empty %}
                <div class="h-50px" style="background-image:url({{ paths('IMAGE_SYSTEM') }}?oid={{ customerEntry.page.coverImageId }}); background-position: center; background-size: cover; background-repeat: no-repeat;"></div>
                {% else %}
                    <div class="h-50px"></div>
                {% endif %}
                <!-- Card body START -->
                <div class="card-body pt-0 ps-4 ps-md-0">
                    <div class="text-center">
                        <!-- Avatar -->
                        <div class="avatar avatar-lg mt-n5 mb-3 rounded-circle">
                            <a href="#!">
                                {% if customerEntry.page is not empty and customerEntry.page.profileImageId is not empty %}
                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ customerEntry.page.profileImageId }}" alt="">
                                {% else %}
                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="">
                                {% endif %}                                    
                            </a>
                        </div>
                        <!-- Info -->
                        {% if customerEntry.page is not empty %}
                        <h5 class="mb-0"> <a href="{{ paths('PAGE_BASE') }}/{{ customerEntry.page.identifier }}">{{ customerEntry.page.name }} {{ customerEntry.page.lastname }} </a> </h5>
                        {% endif %}
                        
                        <p class="mt-3">
                            {{ customerEntry.customer.bio }}
                        </p>

                        <!-- User stat START -->
                        <div class="hstack gap-2 gap-xl-3 justify-content-center">
                            <!-- User stat item -->
                            <div>
                                <!--eventi inseriti in tutte le pagine con il mio utente-->
                                <h6 class="mb-0">{{ customerEntry.userEventCount }}</h6>
                                <small>{{ label('common.events') | raw }}</small>
                            </div>
                            {% if customerEntry.page is not empty %}
                            <!-- Divider -->
                            <div class="vr"></div>
                            <!-- User stat item -->
                            <div>
                                <!--utenti che seguono la mia pagina personale-->
                                <h6 class="mb-0">{{ customerEntry.pageFollower }}</h6>
                                <small>Follower</small>
                            </div>
                            {% endif %}
                            <!-- Divider -->
                            <div class="vr"></div>
                            <!-- User stat item -->
                            <div>
                                <!--utenti che seguo con il mio profilo personale-->
                                <h6 class="mb-0">{{ customerEntry.pageFollowed }}</h6>
                                <small>{{ label('common.followed') | raw }}</small>
                            </div>
                        </div>
                        <!-- User stat END -->
                    </div>

                    <!-- Divider -->
                    <hr>

                    <!-- Side Nav START -->
                    <ul class="nav nav-link-secondary flex-column fw-bold gap-2">
                        <li class="nav-item">
                            <a class="btn btn-primary-soft w-100 fw-bold" href="{{ paths('WALL') }}"> <i class="bi bi-newspaper fa-fw me-2"></i><span>{{ label('common.feed') | raw }} </span></a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary-soft w-100 fw-bold" href="{{ paths('EVENTS') }}"> <i class="bi bi-calendar-day fa-fw me-2"></i> <span>{{ label('common.discover.events') | raw }} <span class="small">Beta</span></span></a>
                        </li>           
                        <hr>
                        <li class="nav-item">
                            <a class="nav-link d-flex mb-0 {{ activePage == 'INFO' ? 'active' : '' }}" href="{{ paths('ACCOUNT_INFO') }}"> <i class="bi bi-person fa-fw me-2"></i><span>Account </span></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-flex mb-0 {{ activePage == 'PAGES' ? 'active' : '' }}" href="{{ paths('ACCOUNT_PAGES') }}"> <i class="bi bi-person-vcard-fill me-2"></i><span>{{ label('common.manage') | raw }} {{ label('common.pages') | raw | lower }} </span></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-flex mb-0 {{ activePage == 'CALENDAR' ? 'active' : '' }}" href="{{ paths('ACCOUNT_CALENDAR') }}"> <i class="bi bi-calendar-week-fill me-2"></i><span>{{ label('common.manage') | raw }} {{ label('common.events') | raw | lower }} </span></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-flex mb-0 {{ activePage == 'NOTIFICATIONS' ? 'active' : '' }}" href="{{ paths('ACCOUNT_NOTIFICATIONS') }}"> <i class="bi bi-bell-fill me-2"></i><span>{{ label('common.notifications') | raw }} </span></a>
                        </li>                        
                    </ul>
                    <!-- Side Nav END -->
                </div>
                <!-- Card body END -->                
            </div>
            <!-- Card END -->
            </div>
    </div>
</nav>
<!-- Navbar END-->
