{% extends "fe/include/base.html" %}

{% block title %}{{ label('event.add.title.meta') | raw }} | Agorapp{% endblock %}

{% block pagecss %}   
<link href="{{ contextPath }}/fe/css/slim.min.css" rel="stylesheet" type="text/css" media="all">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/tiny-slider/dist/tiny-slider.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/choices.js/public/assets/styles/choices.min.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/glightbox-master/dist/css/glightbox.min.css">    
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">
<link href="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.css" rel="stylesheet">
<link href="https://www.siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<a id="eventAddUri" style="display: none" href="{{ paths('EVENT_ADD') }}" rel="nofollow"></a>
<a id="eventAddSaveUri" style="display: none" href="{{ paths('EVENT_ADD_SAVE') }}" rel="nofollow"></a>
<a id="accountInfoUri" style="display: none" href="{{ paths('ACCOUNT_INFO') }}" rel="nofollow"></a>
<a id="accountCalendarUri" style="display: none" href="{{ paths('ACCOUNT_CALENDAR') }}" rel="nofollow"></a>
<a id="backUri" style="display: none" href="{{ backUrl }}" rel="nofollow"></a>
<!-- api call for products autocomplete -->
<a id="dataPagesUri" style="display: none" href="{{ paths('DATA_PAGES') }}?ownerId={{ user.id }}"></a>
<a id="dataEventsUri" style="display: none" href="{{ paths('DATA_SEARCH_EVENTS_CONTAINER') }}"></a>
<a id="dataEventTagUri" style="display: none" href="{{ paths('DATA_TAG_EVENT') }}"></a>
<input type="hidden" id="language" value="{{ language }}">
<input type="hidden" id="isContainer" value="{{ isContainer }}">

<!-- Container START -->
<div class="container">
    <div class="row">

        <!-- Sidenav START -->
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        <!-- End Col -->

        <!-- Main content START -->
        <div class="col-lg-6 vstack gap-4">                               
            <!-- Account settings START -->
            <div class="card border-lg-lr border-lg-b">

                <!-- Card header START -->
                {% if isContainer is not empty and isContainer %}
                <div class="card-header border-0 pb-0">
                    <h1 class="h5 card-title">{{ label('event.add.new.container') | raw }}</h1>
                    <p class="mb-0">{{ label('event.add.info.container') | raw }}</p>
                </div>
                {% else %}
                <div class="card-header border-0 pb-0">
                    <h1 class="h5 card-title">{{ label('event.add.new') | raw }}</h1>
                    <p class="mb-0">{{ label('event.add.info') | raw }}</p>
                </div>
                {% endif %}
                <!-- Card header END -->

                <!-- Card body START -->
                <div class="card-body">
                    <!-- Form START -->
                    <form class="row g-4" id="form-event-add" method="post" novalidate="novalidate">
                        <!-- Cover image -->
                        <div class="col-lg-12">
                            <label class="form-label">{{ label('common.cover.photo') | raw }}</label>
                            {% set originalFilename = '' %}
                            {% if parent is not empty and parent.coverImageId is not empty %}
                            {% set fileDecoded = get('DocumentDescriptor', parent.coverImageId) %}
                            {% set originalFilename = fileDecoded.metadata.originalFilename %}
                            {% endif %}
                            <div class="slim rounded"
                                 data-max-file-size="5"
                                 data-save-initial-image="{{ parent is not empty and parent.coverImageId is not empty ? 'true' : 'false'}}"
                                 data-push="false"
                                 data-post="output"
                                 data-label="{{ label('slim.upload.photo') | raw }}"
                                 data-label-loading=" "
                                 data-ratio="free"
                                 data-jpeg-compression=100
                                 data-button-edit-label="{{ label('common.edit') | raw }}"
                                 data-button-remove-label="{{ label('common.remove') | raw }}"
                                 data-button-download-label="{{ label('common.download') | raw }}"
                                 data-button-upload-label="{{ label('common.upload') | raw }}"
                                 data-button-rotate-label="{{ label('common.rotate') | raw }}"
                                 data-button-cancel-label="{{ label('common.cancel') | raw }}"
                                 data-button-confirm-label="{{ label('common.confirm') | raw }}"
                                 data-status-file-size="{{ label('slim.file.big') | raw }} $0 MB"
                                 data-status-file-type="{{ label('slim.format.not.valid') | raw }} $0"
                                 data-status-no-support="{{ label('slim.browser.not.support') | raw }}"
                                 data-status-image-too-small="{{ label('slim.file.small') | raw }} $0 pixel"
                                 data-status-content-length="{{ label('slim.server.file.big') | raw }}"
                                 data-status-unknown-response="{{ label('slim.unknown.error') | raw }}"
                                 data-status-upload-success="{{ label('slim.image.saved') | raw }}"
                                 data-meta-originalFilename="{{ originalFilename }}">

                                {% if parent.coverImageId is not empty %}
                                <img src="{{ paths('IMAGE_SYSTEM') }}?oid={{ parent.coverImageId }}" alt=""/>
                                {% endif %}
                                <input type="file" id="cropper" name="uploaded-cover" data-show-caption="false" data-show-remove="true">
                            </div>
                            {#<small class="form-text">{{ label('slim.recommended.dimension') | raw }} 1116x280 (ratio 4:1)</small>#}
                        </div>

                        <div class="form-group">
                            <label class="col-lg-3 control-label">{{ label('event.poster') | raw }}:</label>
                            <div class="col-lg-12">
                                <div id="uploader-text" style="display: none;">{{ label('upload.drag.file.here') | raw }}</div>                                
                                <div class="text-center">
                                    <input type="file" name="locandina" data-maxfilessize="4194304" attachment="true" >
                                </div>
                                {% if parent.locandina is not empty %}
                                <div class="panel panel-flat">
                                    <label class="col-lg-3 control-label">{{ label('current.poster') | raw }}:</label>

                                    <div class="panel-body">
                                        <div class="content-group-xs" id="bullets"></div>
                                        {% set fileDecoded = get('DocumentDescriptor', parent.locandina) %}
                                        <div class="media-body">
                                            <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ parent.locandina }}" target="_blank" rel="noopener">
                                                {{ fileDecoded.metadata.originalFilename }}
                                            </a>
                                            <!-- <button type="button" class="btn ml-10 btn-primary mb-0 w-25 delete-file" file-idx="{{ loop.index }}" filegroup="locandina">{{ label('common.remove') | raw }}</button> -->
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                            </div>
                        </div>
                        <!-- End Media -->                            
                        <!-- Category -->
                        {#
                        <div class="col-sm-6">
                            <label class="form-label">{{ label('event.add.category') | raw }}</label>
                            <select class="form-select js-choice" data-search-enabled="true" id="category" name="category" required>
                                <option value="cultura">{{ label('event.category.culture') | raw }}</option>
                                <option value="sport">{{ label('event.category.sport') | raw }}</option>
                                <option value="intrattenimento">{{ label('event.category.entertainment') | raw }}</option>
                                <option value="cibo">{{ label('event.category.food') | raw }}</option>
                                <option value="benessere">{{ label('event.category.health') | raw }}</option>
                                <option value="business">{{ label('event.category.business') | raw }}</option>
                                <option value="comunita">{{ label('event.category.community') | raw }}</option> 
                            </select>
                        </div>
                        #}
                        <!-- Status -->
                        <div class="col-sm-6 hide-for-container">
                            <label class="form-label">{{ label('event.status') | raw }}</label>
                            <select class="form-select js-choice" data-search-enabled="true" id="status" name="status">
                                <option value="EventScheduled">{{ label('event.status.inprogram') | raw }}</option>
                                <option value="EventPostponed">{{ label('event.status.postponed') | raw }}</option>
                                <option value="EventCancelled">{{ label('event.status.cancelled') | raw }}</option>
                                <option value="EventMovedOnline">{{ label('event.status.moved') | raw }}</option>
                            </select>
                        </div>                            
                        <!-- Title -->
                        <div class="col-12">
                            <label class="form-label">{{ label('common.title') | raw }}</label>
                            {% if isContainer is not empty and isContainer %}
                            <input type="text" class="form-control" placeholder="{{ label('event.title.container') | raw }}" id="name" name="name" value="{{ parent is not empty ? parent.name : '' }}" required>
                            {% else %}
                            <input type="text" class="form-control" placeholder="{{ label('event.title') | raw }}" id="name" name="name" value="{{ parent is not empty ? parent.name : '' }}" required>
                            {% endif %}
                        </div>
                        <!-- Description -->
                        <div class="col-12">
                            <label class="form-label">{{ label('common.description') | raw }}</label>
                            <textarea class="form-control summernote" rows="3" placeholder="{{ label('event.description') | raw }}" id="description" name="description">{{ parent is not empty ? parent.description : '' }}</textarea>
                        </div>
                        {# se è un luogo prevedere gestione visibilità in base alla selezione del tipo #}
                        <div class="col-12 hide-for-container">
                            <div class="row g-4 pt-0" id="address-container">
                                <!-- Address -->
                                <div class="col-12">
                                    <label class="form-label">{{ label('common.fulladdress') | raw }} <small>{{ label('common.autocomplete.address') | raw }}</small></label>
                                    <input type="text" class="form-control" placeholder="{{ label('common.address') | raw }}" id="fulladdress" name="fulladdress" required value="{{ parent.fulladdress }}">
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.country') | raw }}</label>
                                    <select class="form-control select-search" name="countryCode" id="countryCode" required>
                                        <option value="">-</option>
                                        {% for item in lookup("country") %}
                                        <option value="{{ item.code }}" {{ item.code == parent.countryCode ? 'selected' : ''}}>{{ item.description }}</option>
                                        {% endfor %}
                                    </select>                                    
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.address') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="address" id="address" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.address') | raw }}" required value="{{ parent.address }}">
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.city') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="city" id="city" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.city') | raw }}" required value="{{ parent.city }}">
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.postalcode') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="10" placeholder="{{ label('common.postalcode') | raw }}" required value="{{ parent.postalCode }}">
                                </div>
                                <div class="col-12 col-sm-6" id="provinceDiv">
                                    <label class="form-label">{{ label('common.province') | raw }}</label>
                                    <select class="form-control select-search provinceCode provinceCodeIt" name="provinceCode" id="provinceCode" required>
                                        <option value="">-</option>
                                        {% for item in lookup("province") %}
                                        <option value="{{ item.code }}" {{ item.code == parent.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                        {% endfor %}
                                    </select>                                    
                                </div>
                                <div class="col-12 col-sm-6" id="provinceExtDiv">
                                    <label class="form-label">{{ label('common.province') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="provinceCode" id="provinceCode" class="form-control maxlength provinceCode provinceCodeExt" placeholder="{{ label('common.province') | raw }}" value="{{ parent.provinceCode }}">
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.extra.city') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="extraAddress" id="extraAddress" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.extra.city') | raw }}" value="{{ parent.extraAddress }}">
                                </div>
                            </div>
                        </div>
                        <!-- Date -->
                        <div class="col-sm-4 hide-for-container">
                            <label class="form-label">{{ label('common.start.date') | raw }}</label>
                            <input type="text" class="form-control flatpickr" id="startDate" name="startDate" placeholder="{{ label('common.start.date') | raw }}" value="{{ parent.startDate | date('dd/MM/yyyy') }}" required>
                            <span id="passed-event-warning" class="d-none custom-error">{{ label('event.date.past.warning') | raw }}</span>
                        </div>
                        <!-- Time -->
                        <div class="col-sm-2 hide-for-container">
                            <label class="form-label">{{ label('common.start.hour') | raw }}</label>
                            <input type="text" id="startHour" name="startHour" class="form-control flatpickr" data-enableTime="true" data-noCalendar="true" placeholder="{{ label('common.hours') | raw }}" required>
                        </div>
                        <!-- Date -->
                        <div class="col-sm-4 hide-for-container">
                            <label class="form-label">{{ label('common.end.date') | raw }}</label>
                            <input type="text" id="endDate" name="endDate" class="form-control flatpickr" placeholder="{{ label('common.end.date') | raw }}">
                        </div>
                        <!-- Time -->
                        <div class="col-sm-2 hide-for-container">
                            <label class="form-label">{{ label('common.end.hour') | raw }}</label>
                            <input type="text" id="endHour" name="endHour" class="form-control flatpickr" data-enableTime="true" data-noCalendar="true" placeholder="{{ label('common.hours') | raw }}">
                        </div>
                        <!-- Ticket url -->
                        <div class="col-md-4 hide-for-container">
                            <label class="form-label">{{ label('event.ticket.url') | raw }}</label>
                            <input type="text" class="form-control" id="ticketsUrl" name="ticketsUrl" placeholder="{{ label('event.url.ticket.buy') | raw }}">
                        </div>
                        <!-- Price -->
                        <div class="col-md-4 hide-for-container">
                            <label class="form-label">{{ label('event.ticket.price') | raw }}</label>
                            <div class="input-group">
                                <span class="input-group-text">€</span>
                                <input type="number" min="0" max="999999" class="form-control apply-changes" name="ticketsMinPrice" id="ticketsMinPrice" placeholder="{{ label('common.price') | raw }}" aria-label="{{ label('common.price') | raw }}" value="{{ parent.ticketsMinPrice | numberformat("#0") }}">
                                <span class="input-group-text rounded-1-end">,00</span>
                                <span class="invalid-feedback">{{ label('event.ticket.price.required') | raw }}</span>
                            </div>
                        </div>
                        <!-- Free -->
                        <div class="col-md-4 hide-for-container">
                            <label class="form-label">{{ label('event.ticket.free') | raw }}</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch"  id="freeEntry" name="freeEntry" {{ parent.freeEntry == true ? 'checked' : '' }}>
                            </div>
                        </div>                        

                        <div class="col-12 hide-for-container">
                            {% set disableField = '' %}
                            {% if parent is not empty %}
                            {% set disableField = 'disabled' %}
                            {% endif %}
                            {% if hasContainer is not empty and hasContainer == false %}
                            {% set disableField = 'disabled' %}
                            {% endif %}
                            <label class="form-label">
                                {{ label('event.container') | raw }}
                                {% if disableField is not empty %}
                                <i class="bi bi-question-circle"
                                   data-bs-toggle="tooltip"
                                   title="{{ label('event.container.hint.disabled') | raw }}">
                                </i>
                                {% else %}
                                <i class="bi bi-question-circle"
                                   data-bs-toggle="tooltip"
                                   title="{{ label('event.container.hint') | raw }}">
                                </i>
                                {% endif %}
                            </label>
                            <select id="parentId" class="form-control" name="parentId" {{ disableField }}>
                                {% if parent is not empty %}
                                <option value="{{ parent.id }}">{{ parent.name }}</option>
                                {% endif %}
                            </select>
                        </div>

                         <div class="col-12 hide-for-container">
                            <label class="form-label">{{ label('event.pages.tags') | raw }}
                                <i class="bi bi-question-circle" 
                                   data-bs-toggle="tooltip" 
                                   title="{{ label('event.pages.tags.hint') | raw }}">
                                </i>
                            </label>
                            <select class="select-search-multiple form-control" id="tags" name="tags" data-placeholder="{{ label('tags.placeholder.multiple') | raw }}" multiple>
                                {% if parent is not empty and parent.tags is not empty %}
                                {% for tag in parent.tags %}
                                <option value="{{ tag }}" selected>{{ tag | lower }}</option>
                                {% endfor %}
                                {% endif %}
                            </select>
                        </div>

                        {% set initialPages = '' %}
                        {% if fromPageId is not empty %}
                        {% set pageRelated = get('page', fromPageId) %}
                        {% set initialPages = initialPages ~ '{"id": "' ~ fromPageId ~ '", "text": "' ~ pageRelated.name ~ '"}' %}
                        {% endif %}
                        {% if parent is not empty %}
                        {% if parent.initialPageIds is not empty %}
                        {% for pageId in parent.initialPageIds %}
                        {% set pageRelated = get('page', pageId) %}
                        {% set initialPages = initialPages ~ (initialPages is not empty ? ',' : '') ~ '{"id": "' ~ pageId ~ '", "text": "' ~ pageRelated.name ~ '"}' %}
                        {% endfor %}
                        {% endif %}
                        {% endif %}
                        <div id="pageInitials" style="display: none;">[{{ initialPages }}]</div>
                        <div class="col-sm-12">
                            <label class="form-label">{{ label('event.pages.involved') | raw }}
                                <i class="bi bi-question-circle" 
                                   data-bs-toggle="tooltip" 
                                   title="{{ label('event.pages.involved.hint') | raw }}">
                                </i>
                            </label>                                
                            <select id="pageIds" class="form-control" name="pageIds" multiple="multiple" required data-tags="true">
                                <!-- ...altri tag... -->
                            </select>
                        </div>
                        <div class="col-sm-12 d-none">
                            <!-- Container per i tag selezionati -->
                            <div id="selected-pages-container"></div>                            
                        </div>
                        <!-- Show Followers -->
                        <div class="col-md-4 hide-for-container">
                            <label class="form-label">{{ label('event.interested.show') | raw }}</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" id="showFollowers" name="showFollowers" {{ parent.showFollowers is empty or parent.showFollowers == false ? '' : 'checked' }}>
                            </div>
                        </div>
                        <!-- Divider -->
                        <hr>
                        <!-- Button  -->
                        <div class="col-12 text-end">
                            {% if isContainer is not empty and isContainer %}
                            <button type="submit" class="btn btn-primary mb-0 w-100">{{ label('common.create.event.container') | raw }}</button>
                            {% else %}
                            <button type="submit" class="btn btn-primary mb-0 w-100">{{ label('common.create.event') | raw }}</button>
                            {% endif %}
                        </div>                            
                    </form>
                    <!-- Form END -->
                </div>
                <!-- Card body END -->
            </div>
            <!-- Account settings END -->                   

        </div>
    </div> <!-- Row END -->
</div>
<!-- Container END -->

{% endblock %}       

{% block pagescripts %}   
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&libraries=places&language=it-IT&callback=initAutocomplete"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/fe/js/slim.kickstart.min.js"></script>    
<script src="{{ contextPath }}/fe/vendor/choices.js/public/assets/scripts/choices.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/glightbox-master/dist/js/glightbox.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.min.js"></script>        
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/l10n/it.js"></script>        
<script src="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/summernote/lang/summernote-it-IT.js"></script>
<script src="{{ contextPath }}/fe/js/pages/event-add.js?{{ buildNumber }}"></script>        
{% endblock %}