package com.agora.extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Pebble function to calculate time difference between lastUpdate date and current date.
 * Returns one of three string values based on the time elapsed:
 * - "recent" for updates within 7 days
 * - "week" for updates more than 7 days but less than 30 days ago
 * - "month" for updates more than 30 days ago
 *
 * <AUTHOR> Augster
 */
public class DaysSinceUpdateFunction implements Function {
    
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("date");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        Date lastUpdateDate = null;
        try {
            lastUpdateDate = (Date) args.get("date");
        } catch (Exception ex) {
            logger.error("Unconvertable date " + args.get("date"), ex);
        }
        
        if (lastUpdateDate == null) {
            return "month"; // Default to oldest category if date is null
        }
        
        Date now = new Date();
        long diffInMillis = now.getTime() - lastUpdateDate.getTime();
        long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);
        
        if (diffInDays <= 7) {
            return "recent";
        } else if (diffInDays <= 30) {
            return "week";
        } else {
            return "month";
        }
    }
}
