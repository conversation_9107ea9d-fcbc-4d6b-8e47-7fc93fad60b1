package com.agora.dao;

import com.agora.commons.CacheCommons;
import com.agora.commons.StorageCommons;
import com.agora.core.Manager;
import com.agora.pojo.Event;
import com.agora.pojo.Page;
import com.agora.pojo.Province;
import com.agora.pojo.types.FileType;
import com.agora.pojo.types.ImageType;
import com.agora.pojo.types.ResultSortType;
import com.agora.support.CityEntry;
import com.agora.support.ProvinceEntry;
import com.agora.support.file.posted.PostedFile;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import com.github.slugify.Slugify;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.*;
import com.mongodb.client.result.UpdateResult;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.security.InvalidParameterException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;

import static com.mongodb.client.model.Accumulators.min;
import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.*;
import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Projections.*;
import static com.mongodb.client.model.Sorts.*;

/**
 *
 * <AUTHOR>
 */
public class EventDao {

    public static Event loadEvent(ObjectId eventId) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        Document doc = collection.find(eq("_id", eventId)).first();
        return Manager.fromDocument(doc, Event.class);
    }

    public static Event loadContainerEvent(ObjectId eventId) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        Document doc = collection.find(and(eq("_id", eventId), eq("type", "container"))).first();
        return Manager.fromDocument(doc, Event.class);
    }

    // per ora usata solo per cache, quindi metto controllo su cancelled (la query parte da event_follower, quindi non riesco a controllare direttamente li)
    public static List<Event> loadEvents(List<ObjectId> eventIds) throws Exception {
        if (eventIds == null) {
            throw new InvalidParameterException("empty eventIds");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection.find(and(in("_id", eventIds), ne("cancelled", true)));
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), ne("statusProgress", "draft")))
                .sort(orderBy(descending("publicationDate"), descending("startDate"), descending("creation")));
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadFutureEventListByPageId(ObjectId pageId) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        Date from = MongoUtils.toComparableDate(TimeUtils.today());

        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), eq("pageIds", pageId), gte("startDate", from)))
                .sort(orderBy(descending("startDate"), descending("creation")));
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadRandomEventList(int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        // Esecuzione della query di aggregazione
        List<Page> result = new ArrayList<>();

        // Costruisci il pipeline di aggregazione
        List<Document> pipeline = Arrays.asList(
                new Document("$match", new Document("isUserPage", true).append("cancelled", new Document("$ne", true))),
                new Document("$sample", new Document("size", limit))
        );

        // Esegui la query di aggregazione
        collection.aggregate(pipeline).map(document -> {
            Page page = new Page();
            try {
                BeanUtils.populate(page, document);
                if (document.containsKey("_id")) {
                    page.setId((ObjectId) document.get("_id"));
                }
            } catch (IllegalAccessException | InvocationTargetException ex) {
                Logger.getLogger(PageDao.class.getName()).log(Level.SEVERE, null, ex);
            }
            return page;
        }).into(result);

        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), ne("statusProgress", "draft")))
                .sort(orderBy(descending("startDate"), descending("creation")));
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventListForCache() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        Date from = MongoUtils.toComparableDate(TimeUtils.today());

        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true),
                        ne("statusProgress", "draft"),
                        gte("startDate", from),
                        ne("coverImageId", null)))
                .sort(orderBy(ascending("startDate")));

        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventListByOwnerId(ObjectId userId) throws Exception {
        return loadEventListByOwnerId(userId, 0, 0);
    }

    public static List<Event> loadEventListByOwnerId(ObjectId userId, int skip, int limit) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), ne("status", "draft"), eq("ownerId", userId)))
                .sort(orderBy(descending("publicationDate"), descending("startDate"), descending("creation")))
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventListByParentId(ObjectId parentId) throws Exception {
        if (parentId == null) {
            throw new InvalidParameterException("empty parentId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), ne("status", "draft"), eq("parentId", parentId)));
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventListByParentIdAndPageIds(ObjectId parentId, List<ObjectId> pageIds, int limit) throws Exception {
        if (parentId == null) {
            throw new InvalidParameterException("empty parentId");
        }
        if (pageIds == null || pageIds.isEmpty()) {
            throw new InvalidParameterException("empty pageIds");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), ne("status", "draft"), eq("parentId", parentId), in("pageIds", pageIds)))
                .sort(orderBy(descending("publicationDate"), descending("startDate"), descending("creation")))
                .limit(limit);
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<String> loadEventTagListBy(String name, int skip, int limit) throws Exception {
        if (name == null) {
            throw new InvalidParameterException("empty name");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        return collection.aggregate(List.of(
                        new Document("$unwind", "$tags"),
                        new Document("$match", new Document("tags", new Document("$regex", name).append("$options", "i"))),
                        new Document("$group", new Document("_id", "$tags")),
                        new Document("$replaceRoot", new Document("newRoot", new Document("tag", "$_id"))),
                        new Document("$sort", new Document("tag", 1)),
                        new Document("$skip", skip),
                        new Document("$limit", limit)
                )).map(doc -> doc.getString("tag"))
                .into(new ArrayList<>());

    }

    public static List<String> loadEventTagList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        return collection.aggregate(List.of(
                new Document("$unwind", "$tags"),
                new Document("$group", new Document("_id", new Document("$toLower", "$tags"))),
                new Document("$replaceRoot", new Document("newRoot", new Document("tag", "$_id")))
        )).map(doc -> doc.getString("tag"))
                .into(new ArrayList<>());

    }

    public static List<String> loadEventFutureTagList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        return collection.aggregate(List.of(
                new Document("$match", new Document("startDate", new Document("$gte", TimeUtils.today()))), // Filtra eventi con startDate >= oggi
                new Document("$unwind", "$tags"),
                new Document("$group", new Document("_id", new Document("$toLower", "$tags"))),
                new Document("$replaceRoot", new Document("newRoot", new Document("tag", "$_id")))
        )).map(doc -> doc.getString("tag"))
                .into(new ArrayList<>());

    }

    public static long removePageIdFromEvent(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();
        filters.add(Filters.eq("pageIds", pageId));  // Filtra documenti dove pageId è presente in pageIds

        Bson update = Updates.pull("pageIds", pageId);

        // Esegui l'aggiornamento
        UpdateResult updateResult = collection.updateMany(Filters.and(filters), update);

        return updateResult.getModifiedCount();

    }

    public static List<Event> loadEventListBy(String name, int skip, int limit) throws Exception {
        return loadEventListBy(name, skip, limit, null);
    }

    public static List<Event> loadEventListBy(String name, int skip, int limit, Bson sort) throws Exception {
        name = StringUtils.isNotBlank(name) ? name : "";
        String nameTag = name;
        
        Slugify slg = new Slugify();
        name = slg.slugify(name);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("status", "draft"));
//        filters.add(or(
//                regex("name", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
//                regex("tags", Pattern.compile("^" + Pattern.quote(name) + "$", Pattern.CASE_INSENSITIVE))
//        ));
        // 07/03/25: usiamo titleIdentifier dato che identifier può essere modificato
        filters.add(or(
                regex("titleIdentifier", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
                regex("tags", Pattern.compile("^" + Pattern.quote(nameTag) + "$", Pattern.CASE_INSENSITIVE))
        ));

        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(sort != null ? sort : orderBy(ascending("name"), descending("creation")))
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventContainerListBy(String name, ObjectId userId, int skip, int limit) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        name = StringUtils.isNotBlank(name) ? name : "";
        String nameTag = name;
        
        Slugify slg = new Slugify();
        name = slg.slugify(name);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("status", "draft"));
        filters.add(eq("type", "container"));
        filters.add(eq("userId", userId));
//        filters.add(or(
//                regex("name", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
//                regex("tags", Pattern.compile("^" + Pattern.quote(name) + "$", Pattern.CASE_INSENSITIVE))
//        ));
        // 07/03/25: usiamo titleIdentifier dato che identifier può essere modificato
        filters.add(or(
                regex("titleIdentifier", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
                regex("tags", Pattern.compile("^" + Pattern.quote(nameTag) + "$", Pattern.CASE_INSENSITIVE))
        ));

        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(ascending("name"), descending("creation")))
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, Event.class);
    }

    public static long loadEventCountBy(String name) throws Exception {
        name = StringUtils.isNotBlank(name) ? name : "";
        String nameTag = name;
        Slugify slg = new Slugify();
        name = slg.slugify(name);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("status", "draft"));
//        filters.add(or(
//                regex("titleIdentifier", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
//                regex("tags", Pattern.compile("^" + Pattern.quote(name) + "$", Pattern.CASE_INSENSITIVE))
//        ));
        filters.add(or(
                regex("titleIdentifier", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
                regex("tags", Pattern.compile("^" + Pattern.quote(nameTag) + "$", Pattern.CASE_INSENSITIVE))
        ));

        return collection.count(and(filters));
    }

    public static long loadEventContainerCountBy(String name, ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        name = StringUtils.isNotBlank(name) ? name : "";
        String nameTag = name;
        Slugify slg = new Slugify();
        name = slg.slugify(name);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("status", "draft"));
        filters.add(eq("type", "container"));
        filters.add(eq("userId", userId));
//        filters.add(or(
//                regex("titleIdentifier", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
//                regex("tags", Pattern.compile("^" + Pattern.quote(name) + "$", Pattern.CASE_INSENSITIVE))
//        ));
        filters.add(or(
                regex("titleIdentifier", Pattern.compile("^.*" + name + ".*", Pattern.CASE_INSENSITIVE)),
                regex("tags", Pattern.compile("^" + Pattern.quote(nameTag) + "$", Pattern.CASE_INSENSITIVE))
        ));

        return collection.count(and(filters));
    }

    public static boolean userHasContainer(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(ne("status", "draft"));
        filters.add(eq("type", "container"));
        filters.add(eq("userId", userId));

        return !collection.find(and(filters)).limit(1).into(new ArrayList<>()).isEmpty();
    }

    public static List<Event> loadEventListByPageId(ObjectId pageId) throws Exception {
        return loadEventListByPageId(pageId, 0, 0);
    }

    public static List<Event> loadEventListByPageId(ObjectId pageId, int skip, int limit) throws Exception {
        return loadEventListByPageId(pageId, skip, limit, null);
    }

    public static List<Event> loadEventListByPageId(ObjectId pageId, int skip, int limit, String sortOrder) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        // Determine sort order - default is descending by startDate, then creation
        // (!) questo sort è stato messo lato java nel file EventCommons.java (!)
        // cercare "sort = orderBy(descending("startDate"), descending("creation"));"
        Bson sort;
        if (StringUtils.equalsIgnoreCase(sortOrder, "asc")) {
            sort = orderBy(ascending("creation"));
        } else {
            // Default: descending by startDate, then creation (existing behavior)
            sort = orderBy(descending("startDate"), descending("creation"));
        }

        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), eq("pageIds", pageId), eq("parentId", null)))
                .sort(sort)
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventListFromContainerByPageId(ObjectId eventId, ObjectId pageId) throws Exception {
        return loadEventListFromContainerByPageId(eventId, pageId, 0, 0);
    }

    public static List<Event> loadEventListFromContainerByPageId(ObjectId eventId, ObjectId pageId, int skip, int limit) throws Exception {
        return loadEventListFromContainerByPageId(eventId, pageId, skip, limit, null);
    }

    public static List<Event> loadEventListFromContainerByPageId(ObjectId eventId, ObjectId pageId, int skip, int limit, String sortOrder) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        // Determine sort order - default is descending by startDate, then creation
        Bson sort;
        if (StringUtils.equalsIgnoreCase(sortOrder, "asc")) {
            sort = orderBy(ascending("creation"));
        } else {
            // Default: descending by startDate, then creation (existing behavior)
            sort = orderBy(descending("startDate"), descending("creation"));
        }

        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), eq("pageIds", pageId), eq("parentId", eventId)))
                .sort(sort)
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventListFromContainerByPageIds(ObjectId eventId, List<ObjectId> pageIds) throws Exception {
        return loadEventListFromContainerByPageIds(eventId, pageIds, 0, 0);
    }

    public static List<Event> loadEventListFromContainerByPageIds(ObjectId eventId, List<ObjectId> pageIds, int skip, int limit) throws Exception {
        return loadEventListFromContainerByPageIds(eventId, pageIds, skip, limit, null);
    }

    public static List<Event> loadEventListFromContainerByPageIds(ObjectId eventId, List<ObjectId> pageIds, int skip, int limit, String sortOrder) throws Exception {
        if (pageIds == null || pageIds.isEmpty()) {
            throw new InvalidParameterException("empty pageIds");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        // Determine sort order - default is descending by startDate, then creation
        Bson sort;
        if (StringUtils.equalsIgnoreCase(sortOrder, "asc")) {
            sort = orderBy(ascending("creation"));
        } else {
            // Default: descending by startDate, then creation (existing behavior)
            sort = orderBy(descending("startDate"), descending("creation"));
        }

        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), in("pageIds", pageIds), eq("parentId", eventId)))
                .sort(sort)
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, Event.class);
    }

    public static long loadEventCountByPageId(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        long count = collection
                .count(and(ne("cancelled", true), eq("pageIds", pageId)));

        return count;
    }

    public static List<Event> loadEventsByPendingPageId(ObjectId pageId) {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        List<Event> events = new ArrayList<>();

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> iterable = collection.find(
                new Document("pendingPageIds", pageId)
        );

        for (Document document : iterable) {
            Event event = Manager.fromDocument(document, Event.class);
            events.add(event);
        }

        return events;
    }

    public static long loadEventCountByUserId(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        long count = collection
                .count(and(ne("cancelled", true), eq("userId", userId)));

        return count;
    }

    public static long loadEventCountByDate(Date date) throws Exception {
        if (date == null) {
            throw new InvalidParameterException("empty date");
        }

        Date from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(date));
        Date to = MongoUtils.toComparableDate(TimeUtils.endOfDay(date));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        gte("creation", from),
                        lte("creation", to))
        );

        return count;
    }

    public static long loadEventCountByLanguage(String language) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        long count = collection.count(
                and(
                        eq("language", language),
                        ne("cancelled", true))
        );

        return count;
    }

    public static List<Event> loadEventListByDateRange(Date from, Date to) throws Exception {
        return loadEventListByDateRange(from, to, 0, 0);
    }

    public static List<Event> loadEventListByDateRange(Date from, Date to, int skip, int limit) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }

        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection.find(and(
                ne("cancelled", true),
                ne("statusProgress", "draft"),
                gte("creation", from),
                lte("creation", to))
        )
                .skip(skip > 0 ? skip : 0)
                .limit(limit > 0 ? limit : 0);

        return Manager.fromDocumentList(list, Event.class);
    }

    public static long changeEventOwnerByPageId(ObjectId pageId, ObjectId oldOwnerId, ObjectId ownerId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        if (ownerId == null) {
            throw new InvalidParameterException("empty ownerId");
        }
        if (oldOwnerId == null) {
            throw new InvalidParameterException("empty oldOwnerId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        // Fai l'aggiornamento multiplo
        UpdateResult updateResult = collection.updateMany(
                Filters.and(
                        Filters.eq("pageIds", pageId),
                        Filters.eq("ownerId", oldOwnerId)
                ),
                Updates.set("ownerId", ownerId));

        return updateResult.getModifiedCount();
    }

    public static List<Event> loadEventListByDateRangeAndUserId(Date from, Date to, ObjectId userId) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection.find(and(
                eq("userId", userId),
                ne("cancelled", true),
                ne("statusProgress", "draft"),
                gte("startDate", from),
                lte("startDate", to))
        );

        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventPublishedList(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        Date dueDate = MongoUtils.toComparableDate(TimeUtils.beginOfDay(new Date()));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection.find(and(
                eq("userId", userId),
                eq("published", true),
                ne("cancelled", true),
                or(eq("statusProgress", "completed"), eq("statusProgress", "published")),
                gte("endDate", dueDate))
        );

        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventCompletedList(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        Date dueDate = MongoUtils.toComparableDate(TimeUtils.beginOfDay(new Date()));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection.find(and(
                eq("userId", userId),
                ne("cancelled", true),
                or(eq("statusProgress", "completed"), eq("statusProgress", "published")),
                gte("endDate", dueDate))
        );

        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventDueList(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        Date dueDate = MongoUtils.toComparableDate(TimeUtils.beginOfDay(new Date()));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        FindIterable<Document> list = collection.find(and(
                eq("userId", userId),
                eq("published", true),
                ne("cancelled", true),
                or(eq("statusProgress", "completed"), eq("statusProgress", "published")),
                lt("endDate", dueDate))
        );

        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<ProvinceEntry> loadEventProvinceList() throws Exception {
        /*
            db.getCollection('property').aggregate(
                [
                    {
                        $match: {
                            provinceCode: {$ne: '', $ne: null}
                        }
                    },
                    {
                        $group: {
                            _id : '$provinceCode',
                            provinceCode: { $min: '$provinceCode'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, provinceCode: 1}},
                    { $sort : { provinceCode : 1 } }
                ]
            )
         */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(
                                ne("provinceCode", ""),
                                ne("provinceCode", null)
                        )),
                        group("$provinceCode", min("provinceCode", "$provinceCode"), sum("count", "1")),
                        project(fields(excludeId(), include("provinceCode"))),
                        sort(orderBy(ascending("provinceCode")))
                ));
        return Manager.fromAggregateList(list, ProvinceEntry.class);
    }

    public static List<CityEntry> loadEventCityList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(
                                ne("city", ""),
                                ne("city", null)
                        )),
                        group("$city", min("city", "$city"), sum("count", "1")),
                        project(fields(excludeId(), include("city"))),
                        sort(orderBy(ascending("city")))
                ));
        return Manager.fromAggregateList(list, CityEntry.class);
    }

    public static long loadEventCountBy(String[] categories, String city, Date fromDate, Date toDate, Boolean singleDate, Province province) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));

        if ((categories != null)
                && (categories.length > 0)) {
            filters.add(in("category", categories));
        }

        if (province != null) {
            filters.add(eq("provinceCode", province.getCode()));
        } else {
            if (StringUtils.isNotBlank(city)) {
                filters.add(eq("city", city));
            }
        }

        if (fromDate != null) {
            filters.add(lte("startDate", toDate));
        }
        if (toDate != null) {
            filters.add(gte("endDate", fromDate));
        }

        if (singleDate != null) {
            filters.add(eq("singleDate", singleDate));
        }
        filters.add(eq("published", true));

        long count = collection.count(
                and(filters)
        );
        return count;
    }

    public static List<Event> loadEventListBy(String[] categories, String city, Date fromDate, Date toDate, int skip, int limit, ResultSortType sortby, Province province, String tag) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));

        if ((categories != null)
                && (categories.length > 0)) {
            filters.add(in("category", categories));
        }

        if (StringUtils.isNotBlank(tag)) {
            filters.add(new Document("tags", new Document("$regex", "^" + Pattern.quote(tag) + "$").append("$options", "i")));
        }

        if (province != null) {
            filters.add(eq("provinceCode", province.getCode()));
        } else {
            if (StringUtils.isNotBlank(city)) {
                filters.add(eq("city", city));
            }
        }

        if (toDate != null) {
            filters.add(lte("startDate", toDate));
        }
        if (fromDate != null) {
            filters.add(gte("startDate", fromDate));
//            filters.add(and(or(
//                gte("endDate", fromDate),    // endDate >= fromDate
//                exists("endDate", false),   // endDate non esiste
//                eq("endDate", null)         // endDate è null
//            )));

        }

        filters.add(ne("status", "draft"));
//        filters.add(eq("published", true));

        Bson sort = orderBy(descending("startDate"));
//        if (sortby != null) {
//            if (Objects.equals(sortby, ResultSortType.name)) {
//                orderBy(descending("creation"), ascending("name"));
//            }
//        }

        FindIterable<Document> list;
        list = collection
                .find(and(filters))
                .skip(skip)
                .limit(limit > 0 ? limit : 0)
                .sort(sort);

        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadFollowEventList(ObjectId userId, int skip, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> pipeline = Arrays.asList(
                Aggregates.match(Filters.ne("cancelled", true)),
                Aggregates.lookup("event_follower", "_id", "eventId", "eventFollowers"),
                Aggregates.unwind("$eventFollowers", new UnwindOptions().preserveNullAndEmptyArrays(true)),
                Aggregates.match(Filters.and(
                        Filters.eq("eventFollowers.userId", userId),
                        Filters.ne("eventFollowers.cancelled", true)
                )),
                Aggregates.sort(Sorts.ascending("startDate")), // Ordina in base alla data di inizio in modo ascendente
                Aggregates.addFields(
                        new Field<>("validEventIds", "$eventFollowers.eventId"),
                        new Field<>("eventId", "$_id") // Aggiunge l'_id dell'evento come nuovo campo
                ),
                Aggregates.replaceRoot(
                        Document.parse("{ $mergeObjects: [ '$$ROOT', { validEventIds: '$validEventIds', _id: '$eventId' } ] }")
                ),
                Aggregates.project(
                        Projections.exclude("eventFollowers")
                ),
                Aggregates.skip(skip),
                Aggregates.limit(limit)
        );

        // Esegui l'aggregazione
        AggregateIterable<Document> result = collection.aggregate(pipeline);

        return Manager.fromAggregateList(result, Event.class);

    }

    public static List<Event> loadWallEventList(ObjectId userId, int skip, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<ObjectId> validPageIds = PageFollowerDao.loadPageFollowedIdListByUser(userId);
        List<Bson> pipeline = Arrays.asList(
                Aggregates.match(and(
                        Filters.ne("cancelled", true),
                        Filters.in("pageIds", validPageIds),
                        Filters.eq("parentId", null)
                )),
                //                Aggregates.lookup("page_follower", "pageIds", "pageId", "pageFollowers"),
                //                Aggregates.unwind("$pageFollowers"),
                //                Aggregates.match(Filters.and(
                //                        Filters.eq("pageFollowers.userId", userId),
                //                        Filters.ne("pageFollowers.cancelled", true)
                //                )),
                //                Aggregates.group("$_id",
                //                        Accumulators.first("event", "$$ROOT"),
                //                        Accumulators.addToSet("validPageIds", "$pageFollowers.pageId")
                //                ),
                //                Aggregates.replaceRoot(
                //                        Document.parse("{ $mergeObjects: [ '$event', { validPageIds: '$validPageIds' } ] }")
                //                ),
                // Filtro aggiunto per confrontare 'creation' con 'startDate'
                // data creazione <= alla data evento

                // MARCO: DATO CHE ORA startDate VIENE POPOLATO CON IL MIN QUESTO CONTROLLO NON DOVREBBE PIU SERVIRE
                /*Aggregates.match(Filters.or(
                        Filters.expr(new Document("$lte", Arrays.asList("$creation", "$startDate"))),
                        Filters.and(
                                Filters.eq("startDate", null),
                                Filters.eq("type", "container")
                        )
                )),*/
                Aggregates.sort(orderBy(descending("publicationDate"), descending("startDate"), descending("creation"))),
                Aggregates.skip(skip),
                Aggregates.limit(limit)
        );

        List<Event> result = new ArrayList<>();

        collection.aggregate(pipeline).map(document -> {
            Event event = new Event();
            try {
                BeanUtils.populate(event, document);
                if (document.containsKey("_id")) {
                    event.setId((ObjectId) document.get("_id"));
                }
                if (event.getPageIds() != null && !event.getPageIds().isEmpty()) {
                    event.getPageIds().stream().filter(pageId -> (validPageIds.contains(pageId))).forEachOrdered(pageId -> {
                        if (event.getValidPageIds() == null) {
                            event.setValidPageIds(new ArrayList<>());
                        }
                        event.getValidPageIds().add(pageId);
                    });
                }
            } catch (IllegalAccessException | InvocationTargetException ex) {
                Logger.getLogger(EventDao.class.getName()).log(Level.SEVERE, null, ex);
            }
            return event;
        }).into(result);

        return result;
    }

    public static List<Event> loadRandomWallEventList(int skip, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> pipeline = Arrays.asList(
                Aggregates.match(and(Filters.ne("cancelled", true), gte("startDate", TimeUtils.beginOfDay(new Date())), lte("startDate", DateUtils.addMonths(new Date(), 1)))),
                Aggregates.sort(Sorts.ascending("startDate")),
                Aggregates.skip(skip),
                Aggregates.limit(limit)
        );

        List<Event> result = new ArrayList<>();

        collection.aggregate(pipeline).map(document -> {
            Event event = new Event();
            try {
                BeanUtils.populate(event, document);
                if (document.containsKey("_id")) {
                    event.setId((ObjectId) document.get("_id"));
                }
            } catch (IllegalAccessException | InvocationTargetException ex) {
                Logger.getLogger(EventDao.class.getName()).log(Level.SEVERE, null, ex);
            }
            return event;
        }).into(result);

        return result;
    }

    public static List<Event> loadEventListWherePublished() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));

        filters.add(eq("published", true));

        Bson sort = orderBy(descending("sortingDate"), ascending("startDate"));

        FindIterable<Document> list;
        list = collection
                .find(and(filters))
                .sort(sort);

        return Manager.fromDocumentList(list, Event.class);
    }

    public static List<Event> loadEventListByCategoryExcludeOne(String category, Date fromDate, ObjectId eventId, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));
        if (category != null) {
            filters.add(eq("category", category));
        }
        if (fromDate != null) {
            filters.add(gte("startDate", fromDate));
        }
        if (eventId != null) {
            filters.add(ne("_id", eventId));
        }
        filters.add(eq("published", true));

        Bson sort = orderBy(descending("startDate"));
//        if (sortby != null) {
//            if (Objects.equals(sortby, ResultSortType.name)) {
//                orderBy(descending("creation"), ascending("name"));
//            }
//        }

        FindIterable<Document> list;
        list = collection
                .find(and(filters))
                .limit(limit > 0 ? limit : 0)
                .sort(sort);

        return Manager.fromDocumentList(list, Event.class);
    }

    public static Event loadEventByIdentifier(String identifier) throws Exception {
        if (identifier == null) {
            throw new InvalidParameterException("empty identifier");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        Document doc = collection.find(and(ne("cancelled", true), eq("identifier", identifier))).first();
        Event event = Manager.fromDocument(doc, Event.class);
        if (event != null && event.getParentId() != null && (event.getLocandina() == null || event.getCoverImageId() == null)) {
            // carico locandina e/o cover image dal padre
            Event parent = loadEvent(event.getParentId());
            if (parent != null) {
                if (parent.getLocandina() != null && event.getLocandina() == null) {
                    event.setLocandina(parent.getLocandina());
                }
                if (parent.getCoverImageId() != null && event.getCoverImageId() == null) {
                    event.setCoverImageId(parent.getCoverImageId());
                }
            }
        }
        return event;
    }

    public static Event loadEventByQrCode(String qrcode) throws Exception {
        if (qrcode == null) {
            throw new InvalidParameterException("empty qrcode");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        Document doc = collection.find(and(eq("qrcode", qrcode), ne("cancelled", true))).first();
        return Manager.fromDocument(doc, Event.class);
    }

    public static List<Event> loadHomeEvents(List<ObjectId> excludedIds) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("wall_event_ranking");
        FindIterable<Document> list;
        if (excludedIds != null) {
            list = collection
                    .find(nin("_id", excludedIds))
                    .sort(orderBy(descending("ranking"), descending("_id")))
                    .limit(25);
        } else {
            list = collection
                    .find()
                    .sort(orderBy(descending("ranking"), descending("_id")))
                    .limit(25);
        }

        List<ObjectId> eventIds = new ArrayList<>();
        for (Document doc : list) {
            eventIds.add(doc.getObjectId("_id"));
        }
        if (!eventIds.isEmpty()) {
            return loadEvents(eventIds);
        } else {
            return null;
        }
    }

    public static ObjectId insertEvent(Event event) throws Exception {
        if (event == null) {
            throw new InvalidParameterException("empty event");
        }

        // defaults
        Date now = new Date();

        // internals
        event.setCreation(now);
        event.setLastUpdate(now);

        // aggiornamento titleIdentifier usato nella ricerca
        if (StringUtils.isNotBlank(event.getName())) {
            Slugify slg = new Slugify();
            event.setTitleIdentifier(slg.slugify(event.getName()));
        } else {
            event.setTitleIdentifier(null); // forse non sarebbe neanche da fare ?
        }

        // gestione data di pubblicazione
        // event.setPublicationDate(event.getStartDate() != null ? event.getStartDate() : now);
        event.setPublicationDate(now);
        if (event.getStartDate() == null) {
            event.setStartDate(event.getPublicationDate());
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        Document doc = Manager.toDocument(event);
        collection.insertOne(doc);

        // altrimenti sotto quando controllo se non c'è nel parent mette null
        event.setId(doc.getObjectId("_id"));
        if (event.getParentId() != null) {
            // devo aggiornare il padre
            Event parent = loadEvent(event.getParentId());
            if (parent != null) {
                List<Event> childEvents = loadEventListByParentId(event.getParentId());
                if (childEvents != null && !childEvents.isEmpty()) {
                    parent.setPageIds(new ArrayList<>());
                    parent.setPendingPageIds(new ArrayList<>());
                    parent.setChildIds(new ArrayList<>());
                    for (Event child : childEvents) {
                        if (child.getId() != null) { // per sicurezza ma credo sia inutile
                            parent.getChildIds().add(child.getId());
                            if (child.getPageIds() != null && !child.getPageIds().isEmpty()) {
                                List<Page> pageList = PageDao.loadPages(child.getPageIds());
                                if (pageList != null && !pageList.isEmpty()) {
                                    for (Page page : pageList) {
                                        // Check if page requires approval
                                        if (page != null) {
                                            if (!StringUtils.equalsIgnoreCase(page.getPageTagging(), "everyone")) {
                                                if (!parent.getPendingPageIds().contains(page.getId())) {
                                                    parent.getPendingPageIds().add(page.getId());
                                                }
                                            } else {
                                                if (!parent.getPageIds().contains(page.getId())) {
                                                    parent.getPageIds().add(page.getId());
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (!parent.getChildIds().contains(event.getId())) {
                        parent.getChildIds().add(event.getId());
                    }
                    if (event.getPageIds() != null && !event.getPageIds().isEmpty()) {
                        List<Page> pageList = PageDao.loadPages(event.getPageIds());
                        if (pageList != null && !pageList.isEmpty()) {
                            for (Page page : pageList) {
                                // Check if page requires approval
                                if (page != null) {
                                    if (!StringUtils.equalsIgnoreCase(page.getPageTagging(), "everyone")) {
                                        if (!parent.getPendingPageIds().contains(page.getId())) {
                                            parent.getPendingPageIds().add(page.getId());
                                        }
                                    } else {
                                        if (!parent.getPageIds().contains(page.getId())) {
                                            parent.getPageIds().add(page.getId());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // nuova gestione container: se sto creando un evento figlio di un container
                    // allora aggiorno la data di pubblicazione anche del parent (così che poi torna in evidenza)
                    parent.setPublicationDate(now);
                    if (StringUtils.equalsIgnoreCase(parent.getType(), "container")) {
                        /*parent.setStartDate(now);
                        if (event.getEndDate() != null && (parent.getEndDate() == null || event.getEndDate().after(parent.getEndDate()))) {
                            parent.setEndDate(event.getEndDate());
                        }*/

                        // calculate min start date from child events
                        parent.setStartDate(null);
                        for (Event child : childEvents) {
                            if (child.getStartDate() != null) {
                                if (parent.getStartDate() == null || child.getStartDate().before(parent.getStartDate())) {
                                    parent.setStartDate(child.getStartDate());
                                }
                            }
                        }
                        // calculate max end date from child events
                        parent.setEndDate(null);
                        for (Event child : childEvents) {
                            if (child.getEndDate() != null) {
                                if (parent.getEndDate() == null || child.getEndDate().after(parent.getEndDate())) {
                                    parent.setEndDate(child.getEndDate());
                                }
                            }
                        }
                    }

                    // per essere sicuro di evitare ricorsione infinita
                    updateEvent(parent, true);
                }
            }
        }

        // chiamata asincrona, non dovrebbe essere bloccante
        CacheCommons.reloadMostUsedTags();
        
        return doc.get("_id", ObjectId.class);
    }

    public static ObjectId insertWallEvent(ObjectId eventId, Double ranking) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        if (ranking == null) {
            throw new InvalidParameterException("empty ranking");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("wall_event_ranking");
        Document doc = new Document("_id", eventId);
        doc.append("ranking", ranking);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }


    public static void updateEvent(Event event) throws Exception {
        updateEvent(event, false);
    }

    public static void updateEvent(Event event, Boolean skipParentCheck) throws Exception {
        if (event == null) {
            throw new InvalidParameterException("empty event");
        }

        // defaults
        Date now = new Date();

        // internals
        event.setLastUpdate(now);

        // aggiornamento titleIdentifier usato nella ricerca
        if (StringUtils.isNotBlank(event.getName())) {
            Slugify slg = new Slugify();
            event.setTitleIdentifier(slg.slugify(event.getName()));
        } else {
            event.setTitleIdentifier(null); // forse non sarebbe neanche da fare ?
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        collection.replaceOne(
                new Document("_id", event.getId()),
                Manager.toDocument(event)
        );

        if (BooleanUtils.isFalse(skipParentCheck)) {
            if (event.getParentId() != null) {
                // devo aggiornare il padre
                Event parent = loadEvent(event.getParentId());
                if (parent != null) {
                    List<Event> childEvents = loadEventListByParentId(event.getParentId());
                    if (childEvents != null && !childEvents.isEmpty()) {
                        parent.setPageIds(new ArrayList<>());
                        parent.setPendingPageIds(new ArrayList<>());
                        parent.setChildIds(new ArrayList<>());
                        for (Event child : childEvents) {
                            if (child.getId() != null) { // per sicurezza ma credo sia inutile
                                parent.getChildIds().add(child.getId());
                                if (child.getPageIds() != null && !child.getPageIds().isEmpty()) {
                                    List<Page> pageList = PageDao.loadPages(child.getPageIds());
                                    if (pageList != null && !pageList.isEmpty()) {
                                        for (Page page : pageList) {
                                            // Check if page requires approval
                                            if (page != null) {
                                                if (!StringUtils.equalsIgnoreCase(page.getPageTagging(), "everyone")) {
                                                    if (!parent.getPendingPageIds().contains(page.getId())) {
                                                        parent.getPendingPageIds().add(page.getId());
                                                    }
                                                } else {
                                                    if (!parent.getPageIds().contains(page.getId())) {
                                                        parent.getPageIds().add(page.getId());
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (!parent.getChildIds().contains(event.getId())) {
                            parent.getChildIds().add(event.getId());
                        }
                        if (event.getPageIds() != null && !event.getPageIds().isEmpty()) {
                            List<Page> pageList = PageDao.loadPages(event.getPageIds());
                            if (pageList != null && !pageList.isEmpty()) {
                                for (Page page : pageList) {
                                    // Check if page requires approval
                                    if (page != null) {
                                        if (!StringUtils.equalsIgnoreCase(page.getPageTagging(), "everyone")) {
                                            if (!parent.getPendingPageIds().contains(page.getId())) {
                                                parent.getPendingPageIds().add(page.getId());
                                            }
                                        } else {
                                            if (!parent.getPageIds().contains(page.getId())) {
                                                parent.getPageIds().add(page.getId());
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (StringUtils.equalsIgnoreCase(parent.getType(), "container")) {
                            // calculate min start date from child events
                            parent.setStartDate(null);
                            for (Event child : childEvents) {
                                if (child.getStartDate() != null) {
                                    if (parent.getStartDate() == null || child.getStartDate().before(parent.getStartDate())) {
                                        parent.setStartDate(child.getStartDate());
                                    }
                                }
                            }
                            // calculate max end date from child events
                            parent.setEndDate(null);
                            for (Event child : childEvents) {
                                if (child.getEndDate() != null) {
                                    if (parent.getEndDate() == null || child.getEndDate().after(parent.getEndDate())) {
                                        parent.setEndDate(child.getEndDate());
                                    }
                                }
                            }
                            /*if (event.getEndDate() != null && (parent.getEndDate() == null || event.getEndDate().after(parent.getEndDate()))) {
                                parent.setEndDate(event.getEndDate());
                            }*/
                        }
                        
                        // per essere sicuro di evitare ricorsione infinita
                        updateEvent(parent, true);
                    }
                }
            }
        }

        // chiamata asincrona, non dovrebbe essere bloccante
        CacheCommons.reloadMostUsedTags();
    }

    public static void updateEventCancelled(ObjectId eventId, boolean cancelled) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        // defaults
        Date now = new Date();

        // update
        Event event = loadEvent(eventId);
        event.setCancelled(cancelled);
        updateEvent(event);

        // chiamata asincrona, non dovrebbe essere bloccante
        CacheCommons.reloadMostUsedTags();
    }

    public static List<String> loadEventCategoryList(String language) throws Exception {
        /*
            db.getCollection('event').aggregate(
                [
                    {
                        $group: {
                            _id : '$category',
                            category: { $min: '$category'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, category: 1}},
                    { $sort : { category : 1 } }
                ]
            )
         */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));

        if (StringUtils.isNotBlank(language)) {
            filters.add(eq("language", language));
        }

        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(filters)),
                        group("$category", min("category", "$category"), sum("count", 1)),
                        project(fields(excludeId(), include("category"))),
                        sort(orderBy(ascending("category")))
                ));

        List<String> items = new ArrayList<>();
        if (list != null) {
            for (Document document : list) {
                String value = document.getString("category");
                items.add(value);
            }
        }

        return !items.isEmpty() ? items : null;
    }

    public static List<String> loadEventTagList(String language) throws Exception {
        /*
            db.getCollection('event').aggregate(
                [
                    {
                        $group: {
                            _id : '$tags',
                            category: { $min: '$tags'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, tags: 1}},
                    { $sort : { tags : 1 } }
                ]
            )
         */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));

        if (StringUtils.isNotBlank(language)) {
            filters.add(eq("language", language));
        }

        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(filters)),
                        unwind("$tags"),
                        group("$tags", sum("count", 1)),
                        project(fields(include("tags"))),
                        sort(orderBy(ascending("_id")))
                ));

        List<String> items = new ArrayList<>();
        if (list != null) {
            for (Document document : list) {
                String value = document.getString("_id");
                items.add(value);
            }
        }
        return !items.isEmpty() ? items : null;
    }

    public static List<String> loadMostUsedTagsForCache() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");

        AggregateIterable<Document> result = collection.aggregate(Arrays.asList(
                new Document("$unwind", "$tags"),
                new Document("$group", new Document("_id", "$tags")
                        .append("count", new Document("$sum", 1))),
                new Document("$sort", new Document("count", -1)),
                new Document("$limit", 10)
        ));

        List<String> topTags = new ArrayList<>();
        for (Document doc : result) {
            // Aggiungi solo il tag (campo "_id")
            topTags.add(doc.getString("_id"));
        }

        return topTags;
    }

    public static void updateCoverImage(String username, ObjectId eventId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }

        // defaults
        Date now = new Date();

        String originalFilename = image.getInput().getName();
        if (StringUtils.startsWith(originalFilename, "imagesystem")) {
            if (image.getMeta() != null && image.getMeta() != null) {
                Object metaObject = image.getMeta();

                if (metaObject instanceof LinkedHashMap) {
                    LinkedHashMap<String, Object> metaMap = (LinkedHashMap<String, Object>) metaObject;

                    // Ora puoi accedere ai dati nella mappa
                    Object valueFilename = metaMap.get("originalfilename");
                    // Esegui il cast se necessario
                    if (valueFilename instanceof String) {
                        originalFilename = (String) valueFilename;
                    }
                }
            }
        }

        // defaults
        String imageName = ImageDao.composeFilenameV2(ImageType.eventCover, FilenameUtils.getBaseName(originalFilename), image.getExtension());
        String type = image.getType();

        // save image
        File savedFile = new File(StorageCommons.composePath(StorageCommons.StorageType.img, now, imageName));
        savedFile.getParentFile().mkdirs();
        try ( OutputStream out = new FileOutputStream(savedFile)) {
            out.write(image.getBytes());
        }

        Integer width = null, height = null;
        if (image.getInput() != null) {
            width = image.getInput().getWidth();
            height = image.getInput().getHeight();
        }

        // save image
        ObjectId imageId = ImageDao.insertImageV2(savedFile,
                originalFilename,
                type,
                width,
                height
        );

        // update imageId
        Event event = loadEvent(eventId);
        ObjectId oldImageId = event.getCoverImageId();
        event.setCoverImageId(imageId);

        // internals
        event.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        collection.replaceOne(
                new Document("_id", event.getId()),
                Manager.toDocument(event)
        );

        if (oldImageId != null) {
            ImageDao.deleteImage(oldImageId);
            // Aggiorno tutti gli altri eventi con la stessa immagine
            collection.updateMany(
                    new Document("coverImageId", oldImageId),
                    new Document("$set", new Document("coverImageId", imageId))
            );
        }

    }

    public static ObjectId updateQrcodeImage(String filename, ObjectId eventId, String qrCodeSvg) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        if (qrCodeSvg == null) {
            throw new InvalidParameterException("empty image");
        }

        // defaults
        Date now = new Date();

        // defaults
        String imageName = ImageDao.composeFilenameV2(ImageType.qrcode, filename, "svg");
        String type = "image/svg+xml";

        // save image
        File savedFile = new File(StorageCommons.composePath(StorageCommons.StorageType.qrc, now, imageName));
        savedFile.getParentFile().mkdirs();

        try ( FileWriter fileWriter = new FileWriter(savedFile)) {
            fileWriter.write(qrCodeSvg);
        } catch (IOException e) {
            // Gestisci l'eccezione se si verifica un errore durante la scrittura del file
            e.printStackTrace();
        }

        // save image
        ObjectId imageId = ImageDao.insertImageV2(savedFile,
                filename,
                type,
                null,
                null
        );

        // update imageId
        Event event = loadEvent(eventId);
        ObjectId oldQrcodeId = event.getQrcodeFileId();
        event.setQrcode(filename);
        event.setQrcodeFileId(imageId);

        // internals
        event.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        collection.replaceOne(
                new Document("_id", event.getId()),
                Manager.toDocument(event)
        );

        if (oldQrcodeId != null) {
            ImageDao.deleteImage(oldQrcodeId);
        }
        return imageId;
    }

    public static void removeCoverImage(ObjectId eventId) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        // defaults
        Date now = new Date();

        // update
        Event event = loadEvent(eventId);
        ObjectId oldImageId = event.getCoverImageId();
        event.setCoverImageId(null);

        // internals
        event.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        collection.replaceOne(
                new Document("_id", event.getId()),
                Manager.toDocument(event)
        );

        if (oldImageId != null) {
            ImageDao.deleteImage(oldImageId);
            // Aggiorno tutti gli altri eventi con la stessa immagine
            collection.updateMany(
                    new Document("coverImageId", oldImageId),
                    new Document("$set", new Document("coverImageId", null))
            );
        }

    }

    public static void updateEventLocandina(ObjectId eventId, List<PostedFile> files) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty siteId");
        }
        if (files == null || files.isEmpty()) {
            throw new InvalidParameterException("empty files");
        }
        for (PostedFile file : files) {
            if (StringUtils.isBlank(file.getFilename())) {
                throw new InvalidParameterException("empty filename");
            }
        }

        // defaults
        Date now = new Date();

        Map<String, List<ObjectId>> fileIds = new HashMap<>();
        //List<ObjectId> fileIds = new ArrayList<>();
        for (PostedFile file : files) {
            String filename = FileDao.composeFilenameV2(FileType.attachment, FilenameUtils.getBaseName(file.getName()), file.getExtension());

            // save file
            File fll = new File(file.getFilename());

            // uso Application dato che è sempre presente
            //String projectName = Application.class.getPackage().getName().replace("com.", "").replace(".core", "");
            File savedFile = new File(StorageCommons.composePath(StorageCommons.StorageType.doc, now, filename));
            savedFile.getParentFile().mkdirs();
            try ( OutputStream out = new FileOutputStream(savedFile)) {
                out.write(FileUtils.readFileToByteArray(fll));
            }

            // salvataggio mongo
            ObjectId fileId = FileDao.insertFileV2(savedFile, file.getName(), file.getContentType());

            if (fileId != null) {
                if (!fileIds.containsKey(file.getClassFieldName())) {
                    fileIds.put(file.getClassFieldName(), new ArrayList<>());
                }
                fileIds.get(file.getClassFieldName()).add(fileId);
            }
        }

        // update fileIds
        Event event = loadEvent(eventId);
        ObjectId oldLocandinaId = null;
        if (!fileIds.isEmpty()) {
            for (String fieldClassName : fileIds.keySet()) {
                if (StringUtils.equals(fieldClassName, "locandina")) {
                    oldLocandinaId = event.getLocandina();
                }
                java.lang.reflect.Field field = Event.class.getDeclaredField(fieldClassName);
                field.setAccessible(true);
                ObjectId temporaryFileId = fileIds.get(fieldClassName).get(0);
                field.set(event, temporaryFileId);
            }
        }

        // internals
        event.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        collection.replaceOne(
                new Document("_id", event.getId()),
                Manager.toDocument(event)
        );

        if (oldLocandinaId != null) {
            // Aggiorno tutti gli altri eventi con la stessa locandina
            collection.updateMany(
                    new Document("locandina", oldLocandinaId),
                    new Document("$set", new Document("locandina", event.getLocandina()))
            );
        }
    }

    public static void deleteEvent(ObjectId eventId) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        collection.deleteOne(
                new Document("_id", eventId)
        );
    }

}
