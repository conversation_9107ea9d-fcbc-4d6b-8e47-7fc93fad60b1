package com.agora.commons;

import com.agora.core.Defaults;
import com.agora.customers.CustomerEntry;
import com.agora.dao.CustomerDao;
import com.agora.dao.EventDao;
import com.agora.dao.PageDao;
import com.agora.dao.PageFollowerDao;
import com.agora.dao.UserDao;
import com.agora.pojo.Customer;
import com.agora.pojo.CustomerAddress;
import com.agora.pojo.Page;
import com.agora.pojo.User;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class CustomerCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerCommons.class.getName());

    public static String fullname(Customer customer) {
        String fullname = "*Nome* *Cognome*";
        if (customer != null) {
            if (StringUtils.isNotBlank(customer.getFullname())) {
                fullname = customer.getFullname();
            } else {
                if (StringUtils.isNotBlank(customer.getLastname())) {
                    fullname = customer.getLastname();
                }
                if (StringUtils.isNotBlank(customer.getName())) {
                    if (StringUtils.isBlank(fullname)) {
                        fullname = "";
                    } else {
                        fullname += " ";
                    }
                    fullname += customer.getName();
                }
            }
        }
        return fullname;
    }
    
    public static List<CustomerEntry> toEntries(List<Customer> customerList) {
        List<CustomerEntry> customerEntryList = null;
        if ((customerList != null) &&
            (customerList.size() > 0)) {
            for (Customer customer : customerList) {
                CustomerEntry entry = toEntry(customer);
                if (customerEntryList == null) {
                    customerEntryList = new ArrayList<>();
                }
                customerEntryList.add(entry);
            }   
        }
        return customerEntryList;
    }
    
    public static CustomerEntry toEntry(Customer customer) {
        CustomerEntry entry = null;
        if (customer != null) {
            
            entry = new CustomerEntry();
            entry.setCustomer(customer);
            
            User user = null;
            if (customer.getUserId() != null) {
                try {
                    user = UserDao.loadUser(customer.getUserId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (user != null) {
                    entry.setUser(user);
                }
                
                Page userPage = null;
                try {
                    userPage = PageDao.loadUserPage(customer.getUserId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (userPage != null) {
                    entry.setPage(userPage);
                    
                    long followerCount = 0;
                    try {
                        followerCount = PageFollowerDao.loadPageFollowerCount(userPage.getId());
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    entry.setPageFollower(followerCount);
                    
                    long pageEventCount = 0;
                    try {
                        pageEventCount = EventDao.loadEventCountByPageId(userPage.getId());
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    entry.setPageEventCount(pageEventCount);
                }
                long followedCount = 0;
                try {
                    followedCount = PageFollowerDao.loadPageFollowedCount(customer.getUserId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                entry.setPageFollowed(followedCount);

                long userEventCount = 0;
                try {
                    userEventCount = EventDao.loadEventCountByUserId(customer.getUserId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                entry.setUserEventCount(userEventCount);
            }
        }
        return entry;
    }    
    
    
    public static boolean isValidCustomer(Customer customer, boolean checkEmail) {
        boolean valid = (customer != null) &&
                (StringUtils.isNotBlank(customer.getLastname()) || StringUtils.isNotBlank(customer.getFullname())) &&
                (StringUtils.isNotBlank(customer.getPhoneNumber()) || StringUtils.isNotBlank(customer.getEmail())) &&                
                StringUtils.isNotBlank(customer.getAddress()) &&
                StringUtils.isNotBlank(customer.getCity()) &&
                StringUtils.isNotBlank(customer.getPostalCode()) &&
                (StringUtils.isNotBlank(customer.getProvinceCode()) || !StringUtils.equalsIgnoreCase(customer.getCountryCode(), Defaults.COUNTRY)) &&
                StringUtils.isNotBlank(customer.getChannel()) &&
                (!checkEmail || !exists(customer.getEmail())) &&
                true
                ;
        
        if (!valid) {
            if (customer != null) {
                LOGGER.warn(
                        "customer validation problem:\n" +
                        "- lastname " + customer.getLastname() + "\n" +
                        "- fullname " + customer.getFullname() + "\n" +
                        "- phoneNumber " + customer.getPhoneNumber() + "\n" +
                        "- email " + customer.getEmail() + "\n" +                        
                        "- address " + customer.getAddress() + "\n" +
                        "- city " + customer.getCity() + "\n" +
                        "- postalCode " + customer.getPostalCode() + "\n" +
                        "- provinceCode " + customer.getProvinceCode() + "\n" +
                        "- channel " + customer.getChannel()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "customer validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }
    
    public static boolean isValidCustomerAddress(CustomerAddress address) {
        boolean valid = (address != null) &&
                StringUtils.isNotBlank(address.getFullname()) &&
                StringUtils.isNotBlank(address.getCountryCode()) &&
                StringUtils.isNotBlank(address.getAddress()) &&
                StringUtils.isNotBlank(address.getCity()) &&
                (StringUtils.isNotBlank(address.getProvinceCode()) || !StringUtils.equalsIgnoreCase(address.getCountryCode(), Defaults.COUNTRY)) &&
                StringUtils.isNotBlank(address.getPostalCode()) &&
                StringUtils.isNotBlank(address.getPhoneNumber())
                ;
        
        if (!valid) {
            if (address != null) {
                LOGGER.warn(
                        "customer address validation problem:\n" +
                        "- fullname " + address.getFullname() + "\n" +
                        "- countryCode " + address.getCountryCode() + "\n" +
                        "- address " + address.getAddress() + "\n" +
                        "- city " + address.getCity() + "\n" +
                        "- provinceCode " + address.getProvinceCode() + "\n" +
                        "- postalCode " + address.getPostalCode() + "\n"
                );
            } else {
                LOGGER.warn(
                        "customer address validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }
    
    public static boolean isValidCustomerUpload(Customer customer) {
        boolean valid = (customer != null) &&
                StringUtils.isNotBlank(customer.getLastname()) &&
                StringUtils.isNotBlank(customer.getCountryCode()) &&
                StringUtils.isNotBlank(customer.getAddress()) &&
                StringUtils.isNotBlank(customer.getCity()) &&
                StringUtils.isNotBlank(customer.getPostalCode()) &&
                (StringUtils.isNotBlank(customer.getProvinceCode()) || !StringUtils.equalsIgnoreCase(customer.getCountryCode(), Defaults.COUNTRY)) &&
                StringUtils.isNotBlank(customer.getChannel())
                ;
        
        if (!valid) {
            if (customer != null) {
                LOGGER.warn(
                        "customer validation problem:\n" +
                        "- lastname " + customer.getLastname() + "\n" +
                        "- phoneNumber " + customer.getPhoneNumber() + "\n" +
                        "- countryCode " + customer.getCountryCode() + "\n" +
                        "- address " + customer.getAddress() + "\n" +
                        "- city " + customer.getCity() + "\n" +
                        "- postalCode " + customer.getPostalCode() + "\n" +
                        "- provinceCode " + customer.getProvinceCode() + "\n" +
                        "- channel " + customer.getChannel()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "customer validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }    

    public static boolean exists(ObjectId customerId) {
        boolean exists = false;
        if (customerId != null) {
            Customer customer = null;
            try {
                customer = CustomerDao.loadCustomer(customerId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            exists = (customer != null);
        }
        return exists;
    }
    
    public static boolean exists(String email) {
        boolean exists = false;
        if (StringUtils.isNotBlank(email)) {
            try {
                List<Customer> customers = CustomerDao.loadCustomerListByEmail(email);
                if (!customers.isEmpty()) {
                    if (customers.size() == 1) {
                        Customer customer = customers.get(0);
                        if (customer.getUserId() != null) {
                            User user = UserDao.loadUser(customer.getUserId());
                            if (user != null) {
                                if (!StringUtils.equalsIgnoreCase(email, user.getEmail())) {
                                    exists = true;        
                                }
                            }
                        }
                    } else {
                        exists = true;
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        return exists;
    }
    
    public static boolean existsCustomerEmail(String email) {
        boolean exists = false;
        if (StringUtils.isNotBlank(email)) {
            try {
                List<Customer> customers = CustomerDao.loadCustomerListByEmail(email);
                if (!customers.isEmpty()) {
                    exists = true;
                }
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        return exists;
    }
    public static boolean existsCustomerVatNumber(String vatNumber) {
        boolean exists = false;
        if (StringUtils.isNotBlank(vatNumber)) {
            try {
                List<Customer> customers = CustomerDao.loadCustomerListByVat(vatNumber);
                if (!customers.isEmpty()) {
                    exists = true;
                }
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        return exists;
    }
    public static boolean existsCustomerTin(String tin) {
        boolean exists = false;
        if (StringUtils.isNotBlank(tin)) {
            try {
                List<Customer> customers = CustomerDao.loadCustomerListByTin(tin);
                if (!customers.isEmpty()) {
                    exists = true;
                }
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        return exists;
    }

}
