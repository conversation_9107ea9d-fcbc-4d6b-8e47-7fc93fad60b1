package com.agora.support.xlsupload;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Singleton manager class for handling upload status information
 * Provides thread-safe storage and retrieval of upload progress data
 * 
 * <AUTHOR> Augster
 */
public class UploadStatusManager {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(UploadStatusManager.class);
    
    private static final long CLEANUP_INTERVAL_MINUTES = 30;
    private static final long STATUS_TIMEOUT_HOURS = 2;
    
    private static UploadStatusManager instance;
    private final ConcurrentHashMap<String, UploadStatus> statusMap;
    private final ScheduledExecutorService cleanupExecutor;
    
    private UploadStatusManager() {
        this.statusMap = new ConcurrentHashMap<>();
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
        
        // Schedule periodic cleanup of old entries
        this.cleanupExecutor.scheduleAtFixedRate(
            this::cleanupOldEntries, 
            CLEANUP_INTERVAL_MINUTES, 
            CLEANUP_INTERVAL_MINUTES, 
            TimeUnit.MINUTES
        );
    }
    
    public static synchronized UploadStatusManager getInstance() {
        if (instance == null) {
            instance = new UploadStatusManager();
        }
        return instance;
    }
    
    /**
     * Create a new upload status entry
     */
    public UploadStatus createStatus(String uploadId) {
        if (uploadId == null || uploadId.trim().isEmpty()) {
            throw new IllegalArgumentException("Upload ID cannot be null or empty");
        }
        
        UploadStatus status = new UploadStatus(uploadId);
        statusMap.put(uploadId, status);
        
        LOGGER.debug("Created upload status for uploadId: {}", uploadId);
        return status;
    }
    
    /**
     * Retrieve upload status by ID
     */
    public UploadStatus getStatus(String uploadId) {
        if (uploadId == null || uploadId.trim().isEmpty()) {
            return null;
        }
        
        return statusMap.get(uploadId);
    }
    
    /**
     * Update existing upload status
     */
    public void updateStatus(String uploadId, UploadStatus status) {
        if (uploadId == null || uploadId.trim().isEmpty() || status == null) {
            return;
        }
        
        statusMap.put(uploadId, status);
    }
    
    /**
     * Remove upload status entry
     */
    public void removeStatus(String uploadId) {
        if (uploadId == null || uploadId.trim().isEmpty()) {
            return;
        }
        
        statusMap.remove(uploadId);
        LOGGER.debug("Removed upload status for uploadId: {}", uploadId);
    }
    
    /**
     * Update progress for an upload
     */
    public void updateProgress(String uploadId, int processedRows, String currentOperation) {
        UploadStatus status = getStatus(uploadId);
        if (status != null) {
            status.setProcessedRows(processedRows);
            if (currentOperation != null) {
                status.setCurrentOperation(currentOperation);
            }
        }
    }
    
    /**
     * Add error to upload status
     */
    public void addError(String uploadId, int rowNumber, String errorMessage, String rowData) {
        UploadStatus status = getStatus(uploadId);
        if (status != null) {
            status.addError(rowNumber, errorMessage, rowData);
        }
    }
    
    /**
     * Mark upload as completed
     */
    public void markCompleted(String uploadId) {
        UploadStatus status = getStatus(uploadId);
        if (status != null) {
            status.markCompleted();
        }
    }
    
    /**
     * Mark upload as error
     */
    public void markError(String uploadId) {
        UploadStatus status = getStatus(uploadId);
        if (status != null) {
            status.markError();
        }
    }
    
    /**
     * Start processing for upload
     */
    public void startProcessing(String uploadId, int totalRows) {
        UploadStatus status = getStatus(uploadId);
        if (status != null) {
            status.setTotalRows(totalRows);
            status.startProcessing();
        }
    }
    
    /**
     * Clean up old entries that have exceeded the timeout period
     */
    private void cleanupOldEntries() {
        try {
            Date cutoffTime = new Date(System.currentTimeMillis() - (STATUS_TIMEOUT_HOURS * 60 * 60 * 1000));
            
            statusMap.entrySet().removeIf(entry -> {
                UploadStatus status = entry.getValue();
                Date timeToCheck = status.getEndTime() != null ? status.getEndTime() : status.getStartTime();
                
                if (timeToCheck != null && timeToCheck.before(cutoffTime)) {
                    LOGGER.debug("Cleaning up old upload status for uploadId: {}", entry.getKey());
                    return true;
                }
                return false;
            });
            
        } catch (Exception e) {
            LOGGER.error("Error during cleanup of old upload status entries", e);
        }
    }
    
    /**
     * Get current number of active status entries
     */
    public int getActiveStatusCount() {
        return statusMap.size();
    }
    
    /**
     * Shutdown the cleanup executor (for application shutdown)
     */
    public void shutdown() {
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
