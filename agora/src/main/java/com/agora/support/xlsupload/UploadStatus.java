package com.agora.support.xlsupload;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * POJO class to track upload progress and status information
 * 
 * <AUTHOR> Augster
 */
public class UploadStatus {
    
    public enum Status {
        IDLE,
        PROCESSING, 
        COMPLETED,
        ERROR
    }
    
    public static class ErrorDetail {
        private int rowNumber;
        private String errorMessage;
        private String rowData;
        
        public ErrorDetail() {}
        
        public ErrorDetail(int rowNumber, String errorMessage, String rowData) {
            this.rowNumber = rowNumber;
            this.errorMessage = errorMessage;
            this.rowData = rowData;
        }
        
        public int getRowNumber() {
            return rowNumber;
        }
        
        public void setRowNumber(int rowNumber) {
            this.rowNumber = rowNumber;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public String getRowData() {
            return rowData;
        }
        
        public void setRowData(String rowData) {
            this.rowData = rowData;
        }
    }
    
    private String uploadId;
    private Date startTime;
    private Date endTime;
    private int totalRows;
    private int processedRows;
    private Status status;
    private List<ErrorDetail> errors;
    private String currentOperation;
    
    public UploadStatus() {
        this.errors = new ArrayList<>();
        this.status = Status.IDLE;
        this.startTime = new Date();
    }
    
    public UploadStatus(String uploadId) {
        this();
        this.uploadId = uploadId;
    }
    
    public String getUploadId() {
        return uploadId;
    }
    
    public void setUploadId(String uploadId) {
        this.uploadId = uploadId;
    }
    
    public Date getStartTime() {
        return startTime;
    }
    
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    public Date getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    public int getTotalRows() {
        return totalRows;
    }
    
    public void setTotalRows(int totalRows) {
        this.totalRows = totalRows;
    }
    
    public int getProcessedRows() {
        return processedRows;
    }
    
    public void setProcessedRows(int processedRows) {
        this.processedRows = processedRows;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public List<ErrorDetail> getErrors() {
        return errors;
    }
    
    public void setErrors(List<ErrorDetail> errors) {
        this.errors = errors;
    }
    
    public String getCurrentOperation() {
        return currentOperation;
    }
    
    public void setCurrentOperation(String currentOperation) {
        this.currentOperation = currentOperation;
    }
    
    // Utility methods
    public void addError(int rowNumber, String errorMessage, String rowData) {
        this.errors.add(new ErrorDetail(rowNumber, errorMessage, rowData));
    }
    
    public void incrementProcessedRows() {
        this.processedRows++;
    }
    
    public double getProgressPercentage() {
        if (totalRows == 0) return 0.0;
        return (double) processedRows / totalRows * 100.0;
    }
    
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    public void markCompleted() {
        this.status = Status.COMPLETED;
        this.endTime = new Date();
    }
    
    public void markError() {
        this.status = Status.ERROR;
        this.endTime = new Date();
    }
    
    public void startProcessing() {
        this.status = Status.PROCESSING;
        this.startTime = new Date();
    }
}
