package com.agora.dashboard.login;

import com.agora.commons.CustomerCommons;
import com.agora.commons.NotificationCommons;
import com.agora.commons.UserCommons;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CounterDao;
import com.agora.dao.CustomerDao;
import com.agora.dao.PageDao;
import com.agora.dao.UserDao;
import com.agora.extensions.LabelFunction;
import com.agora.message.MessageSender;
import com.agora.pojo.Customer;
import com.agora.pojo.Page;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.util.MailUtils;
import com.agora.util.ParamUtils;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import com.github.slugify.Slugify;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class LoginController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class.getName());

    //////////////
    // f.e. routes

    public static TemplateViewRoute access = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);        
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }
        
        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));                             
        
        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));
        
        // private / company
        String genderType = StringUtils.defaultIfBlank(request.queryParams("genderType"), "male");
        attributes.put("genderType", genderType);
        
        Boolean isReset = BooleanUtils.toBoolean(request.queryParams("isReset"));
        attributes.put("isReset", isReset);
        
        return Manager.render(Templates.ACCESS, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute register = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);      
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        
        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));  
        
        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));
                
        return Manager.render(Templates.REGISTER, attributes, RouteUtils.pathType(request));
    };
    
    public static Route access_do = (Request request, Response response) -> {
        // language
        String language = RouteUtils.language(request);

        // posted values (multipart fields parsing)
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        String email = null;
        String password = null;
        boolean remember = false;
        for (FileItem field : fields) {
            if (field.isFormField()) {
                switch (field.getFieldName()) {
                    case "email":
                        email = MailUtils.toUnicode(ParamUtils.emptyToNull(field.getString("UTF-8")));
                        break;
                    case "password":
                        password = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "remember":
                        remember = BooleanUtils.toBoolean(field.getString());
                        break;
                    default:
                        LOGGER.warn("received unknown field " + field.getFieldName());
                        break;
                }
            } else {
                // posted files
                switch (field.getFieldName()) {
                    default:
                        LOGGER.warn("received unknown file field " + field.getFieldName());
                        break;
                }
            }
        }

        if (UserCommons.areValidCredentials(email, password)) {
            
            User user = UserDao.loadUserByUsername(email);
            if (UserCommons.isAuthenticated(user, password)) {
                
                // create redis session
                String token = PasswordHash.getCookieSafeSecureRandom(32);
                Manager.createSession(request, response, token, remember);
                Manager.putSession(token, "user", user);

                // trace login
                // ??????

                // remove forgot
                if (user.getRecoverySendDate() != null) {
                    
                    UserDao.removeUserRecovery(user.getId());
                    
                    user = UserDao.loadUser(user.getId());

                    if (user != null) {

                        // update user on redis
                        Manager.putSession(token, "user", user);

                    } else {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Errore in fase di aggiornamento credenziali");
                    }
                    
                }

            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelFunction.description(language, "error.wrong.credentials"));
            }
            
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelFunction.description(language, "error.wrong.credentials"));
        }
        
        return "ok";
        
    };

    public static Route register_send = (Request request, Response response) -> {
        
        // posted values (multipart fields parsing)
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        String genderType = null;
        String fullname = null;
        String name = null;
        String lastname = null;
        String tin = null;
        String vatNumber = null;
        String email = null;
        String emailConfirm = null;
        String password = null;
        String passwordConfirm = null;
        String countryCode = null;
        String address = null;
        String city = null;
        String provinceCode = null;
        String postalCode = null;
        String phoneNumber = null;
        boolean privacy = false;
        for (FileItem field : fields) {
            if (field.isFormField()) {
                switch (field.getFieldName()) {
                    case "genderType":
                        genderType = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "fullname":
                        fullname = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "name":
                        name = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "lastname":
                        lastname = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "tin":
                        tin = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "vatNumber":
                        vatNumber = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "email":
                        email = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "email-confirm":
                        emailConfirm = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "password":
                        password = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "password-confirm":
                        passwordConfirm = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "countryCode":
                        countryCode = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "address":
                        address = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "city":
                        city = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "provinceCode":
                        provinceCode = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "postalCode":
                        postalCode = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "phoneNumber":
                        phoneNumber = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "privacy":
                        privacy = BooleanUtils.toBoolean(field.getString());
                        break;
                    default:
                        LOGGER.warn("received unknown field " + field.getFieldName());
                        break;
                }
            } else {
                // posted files
                switch (field.getFieldName()) {
                    default:
                        LOGGER.warn("received unknown file field " + field.getFieldName());
                        break;
                }
            }
        }

        // language
        String language = RouteUtils.language(request);
        
        if (UserCommons.isValidUserCustomerRegistration(
                email,
                emailConfirm,
                password,
                passwordConfirm,
                genderType,
                name,
                lastname,
                fullname,
                tin,
                vatNumber,
                countryCode,
                address,
                city,
                provinceCode,
                postalCode,
                phoneNumber,
                privacy)) {
            
            User user = UserDao.loadUserByUsername(email);
            if (user == null) {
                
                // defaults
                String encodedPassword = PasswordHash.createHash(password);
                String registrationToken = UUID.randomUUID().toString();
                
                // check for existing customer
                Customer customer = null;
                
                List<Customer> customers = CustomerDao.loadCustomerListByEmail(email);
                if ((customers != null) &&
                        (!customers.isEmpty())) {
                    
                    // ambiguous email, multiple matches
                    if (customers.size() > 1) {
                        //throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Email già presente su più di un cliente");
                        customer = customers.get(0);
                    }
                    
                    // one match but already linked to credentials
                    if (customers.size() == 1) {
                        if (customers.get(0).getUserId() != null) {
                            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Credenziali già presenti su di un altro cliente");
                        }
                    }

                    // found!!!
                    if (customers.size() == 1) {
                        customer = customers.get(0);
                    }
                    
                }
                
                // inserting user
                User adding = new User();
                adding.setEmail(email);
                adding.setUsername(email);
                adding.setPassword(encodedPassword);
                adding.setProfileType(ProfileType.unconfirmed.toString());
                
                adding.setRegistrationSendDate(TimeUtils.now());
                adding.setRegistrationDate(TimeUtils.now());
                adding.setRegistrationToken(registrationToken);
                adding.setRegistered(false);
                        
                ObjectId addingId = UserDao.insertUser(adding);

                // reload user
                user = UserDao.loadUser(addingId);
                if (user == null) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Registrazione non riuscita");
                }
                
                // customer
                if (customer != null) {
                    
                    // merge existing customer
                    customer.setUserId(addingId);
                    //customer.setShop(true);
                    CustomerDao.updateCustomer(customer);
                    
                } else {

                    // insert customer
                    customer = new Customer();
                    //customer.setGenderType(StringUtils.defaultIfBlank(genderType, "male"));
                    
                    // fullname
                    if (StringUtils.isBlank(fullname)) {
                        fullname = StringUtils.normalizeSpace(StringUtils.defaultIfBlank(name, "") + " " + StringUtils.defaultIfBlank(lastname, ""));
                    }
                    customer.setFullname(fullname);
                    if (StringUtils.equalsIgnoreCase(genderType, "other")) {
                        customer.setInvoiceFullname(fullname);
                    }
                    
                    // name / lastname
                    customer.setName(name);
                    if (StringUtils.isBlank(lastname)) {
                        lastname = fullname;
                    }
                    customer.setLastname(lastname);
                    
                    // address
                    customer.setCountryCode(countryCode);
                    customer.setAddress(address);
                    customer.setCity(city);
                    customer.setProvinceCode(provinceCode);
                    customer.setPostalCode(postalCode);
                    customer.setPhoneNumber(phoneNumber);
                    
                    // invoice address
                    customer.setInvoiceCountryCode(countryCode);
                    customer.setInvoiceAddress(address);
                    customer.setInvoiceCity(city);
                    customer.setInvoiceProvinceCode(provinceCode);
                    customer.setInvoicePostalCode(postalCode);
                    
                    customer.setEmail(email);
                    customer.setSinceDate(TimeUtils.today());
                    customer.setPrivacy(privacy);
                    customer.setUserId(addingId);
                    //customer.setShop(true);
                    
                    customer.setTin(tin);
                    customer.setVatNumber(vatNumber);
                    
                    customer.setChannel(Defaults.PORTAL_CHANNEL);
                    
                    int next = CounterDao.next("customer-protocol");
                    customer.setCode("" + next);

                    CustomerDao.insertCustomer(customer);
                    

                }
                
                // send registration message
                if (!NotificationCommons.notifyUserRegistration(request, customer, user, language)) {
                    // throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Registrazione errata");
                }
                
                // update cart
//                Order cart = ShopCommons.loadShopCart(request, response, user);
//                if (cart != null) {
//                    cart = ShopCommons.refreshShoppingCart(request, user, cart);
//                }
//                
                // trace login
                // ??????
                
                // do login
                boolean remember = true;
                
                // create redis session
                String token = PasswordHash.getCookieSafeSecureRandom(32);
                Manager.createSession(request, response, token, remember);
                Manager.putSession(token, "user", user);
                
                
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Indirizzo email già presente");
            }
            
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Registrazione errata");
        }

        return "ok";
    };
    
    public static Route exit_do = (Request request, Response response) -> {
        
        // params
        // ...
        
        String token = Manager.getToken(request);
        if (StringUtils.isNotBlank(token)) {
            // trace logout
            // ??????

            // session destroying
            Manager.destroySession(response, token);
        }

        response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
        return null;
    };
    
    public static TemplateViewRoute account_confirm = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // allow customer only users
        if ((user != null) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        boolean confirmed = false;

        String registrationToken = request.queryParams("registrationToken");
        if (StringUtils.isNotBlank(registrationToken)) {

            User confirming = UserDao.loadUserByRegistrationToken(registrationToken);
            if (confirming != null) {

                if (StringUtils.equalsIgnoreCase(confirming.getProfileType(), ProfileType.unconfirmed.toString())) {

                    // update status
                    confirming.setProfileType(ProfileType.customer.toString());
                    confirming.setRegistered(true);
                    UserDao.updateUser(confirming);

                    
                    // reload
                    user = UserDao.loadUser(confirming.getId());

                    if (StringUtils.isNotBlank(token)) {
                        // update user on redis
                        Manager.putSession(token, "user", user);
                    }
                    customer = CustomerDao.loadCustomerByUserId(confirming.getId());

                    // Page creation removed - users will create pages manually

                    // send welcome message
                    if (!NotificationCommons.notifyUserConfirmation(request, customer, user, language)) {
                        //throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Conferma errata");
                    }

                } else {
                    LOGGER.warn("re-confirming user " + confirming.getUsername());
                }

                confirmed = true;

            } else {
                // ...
            }
        }
        attributes.put("confirmed", confirmed);

        return Manager.render(Templates.ACCOUNT_CONFIRM, attributes, RouteUtils.pathType(request));
    };

    public static Route account_confirm_send = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "Invio riconferma email non autorizzata");
        }

        // logged customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());

        // language
        String language = RouteUtils.language(request);

        // update user
        String registrationToken = UUID.randomUUID().toString();

        user = UserDao.loadUser(user.getId());
        user.setRegistrationSendDate(TimeUtils.now());
        user.setRegistrationDate(TimeUtils.now());
        user.setRegistrationToken(registrationToken);
        user.setRegistered(false);

        UserDao.updateUser(user);

        // reload user
        user = UserDao.loadUser(user.getId());
        if (user == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Salvataggio riconferma email non autorizzata");
        }

        // update user on redis
        Manager.putSession(token, "user", user);

        // send registration message
        if (!NotificationCommons.notifyUserRegistration(request, customer, user, language)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";
    };
    
    public static TemplateViewRoute recover = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);        
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        
        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));                             

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));
        
        return Manager.render(Templates.RECOVER, attributes, RouteUtils.pathType(request));
    };

    public static Route recover_send = (Request request, Response response) -> {

        // posted values (multipart fields parsing)
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        String email = null;
        for (FileItem field : fields) {
            if (field.isFormField()) {
                switch (field.getFieldName()) {
                    case "email":
                        email = MailUtils.toUnicode(ParamUtils.emptyToNull(field.getString("UTF-8")));
                        break;
                    default:
                        LOGGER.warn("received unknown field " + field.getFieldName());
                        break;
                }
            } else {
                // posted files
                switch (field.getFieldName()) {
                    default:
                        LOGGER.warn("received unknown file field " + field.getFieldName());
                        break;
                }
            }
        }

        // language
        String language = RouteUtils.language(request);
        
        if (MessageSender.validEmailAddress(email)) {
            
            User user = UserDao.loadUserByUsername(email);
            if (user != null) {
                
                UserDao.updateUserRecovery(user.getId());
                
                user = UserDao.loadUser(user.getId());
                
                if (user != null) {
                    
                    if (!NotificationCommons.notifyUserRecovery(request, user, email, language)) {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Recupero credenziali non riuscito");
                    }
                    
                    // ok
                    // ...
                    
                } else {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Recupero credenziali non riuscito");
                }
                
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Indirizzo email non trovato");
            }
            
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Indirizzo email errato");
        }

        return "ok";
    };

    //////////////
    // b.e. routes
    
    public static TemplateViewRoute login = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = StringUtils.defaultIfBlank(RouteUtils.language(request), Defaults.LANGUAGE);
        attributes.put("language", language);
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // backUrl
        String backUrl = request.queryParams("backUrl");
        attributes.put("backUrl", backUrl);

        return Manager.render(Templates.LOGIN, attributes, RouteUtils.pathType(request));
    };
    
    public static Route login_do = (Request request, Response response) -> {
        
        // params
        String email = MailUtils.toUnicode(request.queryParams("email"));
        String password = request.queryParams("password");
        String backUrl = request.queryParams("backUrl");
        boolean remember = BooleanUtils.toBoolean(request.queryParams("remember"));

        if (UserCommons.areValidCredentials(email, password)) {
            
            User user = UserDao.loadUserByUsername(email);
            if (UserCommons.isAuthenticated(user, password)) {
                if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) ||
                StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) ||
                StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
                    
                    // create redis session
                    String token = PasswordHash.getCookieSafeSecureRandom(32);
                    Manager.createSession(request, response, token, remember);
                    Manager.putSession(token, "user", user);

                    // trace login
                    // ??????

                    // remove forgot
                    if (user.getRecoverySendDate() != null) {

                        UserDao.removeUserRecovery(user.getId());

                        user = UserDao.loadUser(user.getId());

                        if (user != null) {

                            // update user on redis
                            Manager.putSession(token, "user", user);

                        } else {
                            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.LOGIN));
                        }

                    }

                    if (StringUtils.isNotBlank(backUrl)) {
                        // backUrl navigation
                        response.redirect(backUrl, HttpStatus.SEE_OTHER_303);
                    } else {
                        // navigation
                        response.redirect(RouteUtils.contextPath(request) + Paths.DASHBOARD, HttpStatus.SEE_OTHER_303);
                    }
                } else {
                    response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.LOGIN) + Paths.back(backUrl)) ;
                }

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.LOGIN) + Paths.back(backUrl)) ;
            }
            
        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.LOGIN) + Paths.back(backUrl)) ;
        }

        return null;
    };

    public static Route logout_do = (Request request, Response response) -> {
        
        // params
        // ...
        
        String token = Manager.getToken(request);
        if (StringUtils.isNotBlank(token)) {
            // trace logout
            // ??????

            // session destroying
            Manager.destroySession(response, token);
        }

        response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
        return null;
    };

    public static TemplateViewRoute forgot = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = StringUtils.defaultIfBlank(RouteUtils.language(request), Defaults.LANGUAGE);
        attributes.put("language", language);
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        return Manager.render(Templates.FORGOT, attributes, RouteUtils.pathType(request));
    };

    public static Route forgot_send = (Request request, Response response) -> {
        
        // params
        String email = MailUtils.toUnicode(request.queryParams("email"));
        
        if (MessageSender.validEmailAddress(email)) {
            
            User user = UserDao.loadUserByUsername(email);
            if (user != null) {
                
                UserDao.updateUserRecovery(user.getId());
                
                user = UserDao.loadUser(user.getId());
                
                if (user != null) {
                    
                    if (!NotificationCommons.notifyUserRecovery(request, user, email, Defaults.LANGUAGE)) {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400);
                    }
                    
                    user.setPassword(PasswordHash.createHash(user.getPassword()));
                    UserDao.updateUser(user);

                    // navigation
                    response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.FORGOT));
                    
                } else {
                    response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.FORGOT));
                }
                
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.FORGOT));
            }
            
        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.FORGOT));
        }

        return null;
    };
    
    public static Route error_404 = (Request request, Response response) -> {
        throw Spark.halt(HttpStatus.NOT_FOUND_404);
    };

    ////////////
    // internals
    
    
}
