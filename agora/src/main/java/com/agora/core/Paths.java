package com.agora.core;

import com.agora.util.RouteUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Paths {

    private static final Logger LOGGER = LoggerFactory.getLogger(Paths.class.getName());

    ///////////
    // FRONTEND
    ///////////

    // home
    @LocalizedPath(language = "en", path                                 = "/:language/wall")
    public static final String WALL                                      = "/:language/wall";
    @LocalizedPath(language = "en", path                                 = "/:language/home")
    public static final String HOME                                      = "/:language/home";
    @LocalizedPath(language = "en", path                                 = "/:language/mediakit")
    public static final String MEDIAKIT                                  = "/:language/mediakit";
    @LocalizedPath(language = "en", path                                 = "/:language/events")
    public static final String EVENTS                                    = "/:language/eventi";
    @LocalizedPath(language = "en", path                                 = "/:language/events/:category")
    public static final String EVENTS_CATEGORY                           = "/:language/eventi/:category";
    @LocalizedPath(language = "en", path                                 = "/:language/about")
    public static final String ABOUT                                     = "/:language/chi-siamo";
    //public static final String JOBS                                      = "/:language/lavora-con-noi";
    @LocalizedPath(language = "en", path                                 = "/:language/terms")
    public static final String TOS                                       = "/:language/termini-condizioni";
    @LocalizedPath(language = "en", path                                 = "/:language/sitemap")
    public static final String SITEMAP                                   = "/:language/mappa-sito";
    @LocalizedPath(language = "en", path                                 = "/:language/notification/all-read")
    public static final String NOTIFICATION_ALL_READ                     = "/:language/notification/all-read";
    @LocalizedPath(language = "en", path                                 = "/:language/notification/read")
    public static final String NOTIFICATION_READ                         = "/:language/notification/read";

    // da eliminare!!!
    @LocalizedPath(language = "en", path                                 = "/:language/contacts")
    public static final String CONTACTS                                  = "/:language/contatti";
    @LocalizedPath(language = "en", path                                 = "/:language/contacts/sens")
    public static final String CONTACTS_SEND                             = "/:language/contatti/invia";

    @LocalizedPath(language = "en", path                                 = "/:language/results")
    public static final String RESULTS                                   = "/:language/results";

    // pages
    @LocalizedPath(language = "en", path                                 = "/:language/page/:identifier")
    public static final String PAGE_DETAIL                               = "/:language/pagina/:identifier";
    @LocalizedPath(language = "en", path                                 = "/:language/pagediff")
    public static final String PAGE_DIFF                                 = "/:language/paginadiff";
    @LocalizedPath(language = "en", path                                 = "/:language/pagediff/merge")
    public static final String PAGE_DIFF_MERGE                           = "/:language/paginadiff/merge";
    @LocalizedPath(language = "en", path                                 = "/:language/claim-page")
    public static final String PAGE_CLAIM                                = "/:language/rivendica-pagina";
    @LocalizedPath(language = "en", path                                 = "/:language/claim-page/send")
    public static final String PAGE_CLAIM_SEND                           = "/:language/rivendica-pagina/invia";
    @LocalizedPath(language = "en", path                                 = "/:language/page")
    public static final String PAGE_BASE                                 = "/:language/pagina";
    @LocalizedPath(language = "en", path                                 = "/:language/page-add")
    public static final String PAGE_ADD                                  = "/:language/pagina-nuova";
    @LocalizedPath(language = "en", path                                 = "/:language/page-add/save")
    public static final String PAGE_ADD_SAVE                             = "/:language/pagine-nuova/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/page-edit")
    public static final String PAGE_EDIT                                 = "/:language/pagina-modifica";
    @LocalizedPath(language = "en", path                                 = "/:language/page-edit/save")
    public static final String PAGE_EDIT_SAVE                            = "/:language/pagine-modifica/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/page-follow/add")
    public static final String PAGE_FOLLOWER_TOGGLE                      = "/:language/pagina-segui/aggiungi";
    @LocalizedPath(language = "en", path                                 = "/:language/page-notification/add")
    public static final String PAGE_NOTIFICATION_TOGGLE                  = "/:language/pagina-notifica/aggiungi";
    @LocalizedPath(language = "en", path                                 = "/:language/page-remove")
    public static final String PAGE_REMOVE                               = "/:language/pagina-rimuovi";
    @LocalizedPath(language = "en", path                                 = "/:language/page-event/request")
    public static final String PAGE_EVENT_REQUEST                        = "/:language/pagina-evento/richiedi";
    @LocalizedPath(language = "en", path                                 = "/:language/page-event/request/remove")
    public static final String PAGE_EVENT_REQUEST_REMOVE                 = "/:language/pagina-evento/richiedi/rimuovi";
    @LocalizedPath(language = "en", path                                 = "/:language/page-report/send")
    public static final String PAGE_REPORT_SEND                          = "/:language/pagina-segnala/invia";
    @LocalizedPath(language = "en", path                                 = "/:language/page-release")
    public static final String PAGE_RELEASE                              = "/:language/pagina-rilascia";
    @LocalizedPath(language = "en", path                                 = "/:language/page/iframe/:identifier")
    public static final String PAGE_IFRAME                               = "/:language/pagina/iframe/:identifier";
    @LocalizedPath(language = "en", path                                 = "/:language/page/search/iframe")
    public static final String PAGE_SEARCH_FRAME                         = "/:language/pagina/cerca/iframe";

    // events
    @LocalizedPath(language = "en", path                                 = "/:language/event-add")
    public static final String EVENT_ADD                                 = "/:language/nuovo-evento";
    @LocalizedPath(language = "en", path                                 = "/:language/event-add/save")
    public static final String EVENT_ADD_SAVE                            = "/:language/nuovo-evento/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/event/:identifier")
    public static final String EVENT_DETAIL                              = "/:language/evento/:identifier";
    @LocalizedPath(language = "en", path                                 = "/:language/event")
    public static final String EVENT_BASE                                = "/:language/evento";
    @LocalizedPath(language = "en", path                                 = "/:language/event-follow/add")
    public static final String EVENT_FOLLOWER_TOGGLE                     = "/:language/evento-segui/aggiungi";
    @LocalizedPath(language = "en", path                                 = "/:language/event-notification/add")
    public static final String EVENT_NOTIFICATION_TOGGLE                 = "/:language/evento-notifica/aggiungi";
    @LocalizedPath(language = "en", path                                 = "/:language/event-edit")
    public static final String EVENT_EDIT                                = "/:language/modifica-evento";
    @LocalizedPath(language = "en", path                                 = "/:language/event-edit/save")
    public static final String EVENT_EDIT_SAVE                           = "/:language/modifica-evento/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/event-edit/file/remove")
    public static final String EVENT_EDIT_FILE_REMOVE                    = "/:language/modifica-evento/file/rimuovi";
    @LocalizedPath(language = "en", path                                 = "/:language/event-clone")
    public static final String EVENT_CLONE                               = "/:language/clona-evento";
    @LocalizedPath(language = "en", path                                 = "/:language/event/page/approval")
    public static final String EVENT_PAGE_APPROVAL                       = "/:language/evento/pagina/approvazione";
    @LocalizedPath(language = "en", path                                 = "/:language/event/page/approval/result")
    public static final String EVENT_PAGE_APPROVAL_RESULT                = "/:language/evento/pagina/approvazione/risultato";
    @LocalizedPath(language = "en", path                                 = "/:language/event-remove")
    public static final String EVENT_REMOVE                              = "/:language/rimuovi-evento";
    @LocalizedPath(language = "en", path                                 = "/:language/event-disconnect-children")
    public static final String EVENT_DISCONNECT_CHILDREN                 = "/:language/evento-scollega-figli";
    @LocalizedPath(language = "en", path                                 = "/:language/page-report/send")
    public static final String EVENT_REPORT_SEND                         = "/:language/evento-segnala/invia";
    @LocalizedPath(language = "en", path                                 = "/:language/event-list/ajax")
    public static final String EVENT_LIST_AJAX                           = "/:language/evento-lista/ajax";
    @LocalizedPath(language = "en", path                                 = "/:language/event-pages/ajax")
    public static final String EVENT_PAGES_AJAX                          = "/:language/evento-pagine/ajax";
    @LocalizedPath(language = "en", path                                 = "/:language/event-pages-with-follow/ajax")
    public static final String EVENT_PAGES_WITH_FOLLOW_AJAX              = "/:language/evento-pagine-con-follow/ajax";
    @LocalizedPath(language = "en", path                                 = "/:language/event-navigation/ajax")
    public static final String EVENT_NAVIGATION_AJAX                     = "/:language/evento-navigazione/ajax";

    // notifications
    @LocalizedPath(language = "en", path                                 = "/:language/notifcation-add")
    public static final String NOTIFICATION_ADD                          = "/:language/notifiche-nuovo";
    @LocalizedPath(language = "en", path                                 = "/:language/notifcation-add/save")
    public static final String NOTIFICATION_ADD_SAVE                     = "/:language/notifiche-nuova/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/notifcation-edit")
    public static final String NOTIFICATION_EDIT                         = "/:language/notifica-modifica";
    @LocalizedPath(language = "en", path                                 = "/:language/notifcation-edit/save")
    public static final String NOTIFICATION_EDIT_SAVE                    = "/:language/notifica-modifica/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/notifcation-remove")
    public static final String NOTIFICATION_REMOVE                       = "/:language/rimuovi-notifica";
    @LocalizedPath(language = "en", path                                 = "/:language/notifcation-activate")
    public static final String NOTIFICATION_ACTIVE                       = "/:language/attiva-notifica";
    @LocalizedPath(language = "en", path                                 = "/:language/notifcation/:identifier")
    public static final String NOTIFICATION_DETAIL                       = "/:language/notifica/:identifier";

    // login
    @LocalizedPath(language = "en", path                                 = "/:language/access")
    public static final String ACCESS                                    = "/:language/accesso";
    @LocalizedPath(language = "en", path                                 = "/:language/access/do")
    public static final String ACCESS_DO                                 = "/:language/accesso/invia";
    @LocalizedPath(language = "en", path                                 = "/:language/register")
    public static final String REGISTER                                  = "/:language/registrati";
    @LocalizedPath(language = "en", path                                 = "/:language/register/do")
    public static final String REGISTER_SEND                             = "/:language/registrati/invia";
    @LocalizedPath(language = "en", path                                 = "/:language/logout/do")
    public static final String EXIT_DO                                   = "/:language/uscita/invia";
    @LocalizedPath(language = "en", path                                 = "/:language/forgot")
    public static final String RECOVER                                   = "/:language/recupera";
    @LocalizedPath(language = "en", path                                 = "/:language/forgot/do")
    public static final String RECOVER_SEND                              = "/:language/recupera/invia";

    // account
    @LocalizedPath(language = "en", path                                 = "/:language/account")
    public static final String ACCOUNT                                   = "/:language/account";
    @LocalizedPath(language = "en", path                                 = "/:language/account/remove")
    public static final String ACCOUNT_REMOVE                            = "/:language/account/rimuovi";
    @LocalizedPath(language = "en", path                                 = "/:language/account/delete")
    public static final String ACCOUNT_DELETED                           = "/:language/account/eliminato";
    @LocalizedPath(language = "en", path                                 = "/:language/account/information")
    public static final String ACCOUNT_INFO                              = "/:language/account/informazioni";
    @LocalizedPath(language = "en", path                                 = "/:language/account/information/save")
    public static final String ACCOUNT_INFO_EDIT_SAVE                    = "/:language/account/info/modifica/salva";

    @LocalizedPath(language = "en", path                                 = "/:language/account/pages")
    public static final String ACCOUNT_PAGES                             = "/:language/account/pagine";
    @LocalizedPath(language = "en", path                                 = "/:language/account/pages/primary/save")
    public static final String ACCOUNT_PAGE_SET_PRIMARY                  = "/:language/account/pagine/principale/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/account/notifications")
    public static final String ACCOUNT_NOTIFICATIONS                     = "/:language/account/notifiche";
    @LocalizedPath(language = "en", path                                 = "/:language/account/calendar")
    public static final String ACCOUNT_CALENDAR                          = "/:language/account/calendario";

    @LocalizedPath(language = "en", path                                 = "/:language/account/password/save")
    public static final String ACCOUNT_SECURITY_CHANGE_PASSWORD_SAVE     = "/:language/account/password/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/account/keyword/save")
    public static final String ACCOUNT_SECURITY_KEYWORD_SAVE             = "/:language/account/keyword/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/account/security/username/save")
    public static final String ACCOUNT_SECURITY_USERNAME_SAVE            = "/:language/account/sicurezza/username/salva";
    @LocalizedPath(language = "en", path                                 = "/:language/account/confirm")
    public static final String ACCOUNT_CONFIRM                           = "/:language/account/conferma";
    @LocalizedPath(language = "en", path                                 = "/:language/account/confirm/send")
    public static final String ACCOUNT_CONFIRM_SEND                      = "/:language/account/conferma/invia";



    //////////
    // BACKEND
    //////////

    // dashboard
    public static final String DASHBOARD                                 = "/dashboard";


    // profile
    public static final String PROFILE                                   = "/profile";
    public static final String PROFILE_SAVE                              = "/profile/save";

    // login
    public static final String BE_LOGIN                                  = "/be/login";             // maintained for backwards compatibility
    public static final String LOGIN                                     = "/login";
    public static final String LOGIN_DO                                  = "/login/do";
    public static final String FORGOT                                    = "/forgot";
    public static final String FORGOT_SEND                               = "/forgot/send";
    public static final String LOGOUT_DO                                 = "/logout/do";

    // firm
    public static final String FIRM                                      = "/firm";
    public static final String FIRM_SAVE                                 = "/firm/save";
    public static final String FIRM_RELOAD_PAGES_IDENTIFIER              = "/firm/reload/pages/identifier";
    public static final String FIRM_RELOAD_EVENTS_IDENTIFIER             = "/firm/reload/events/identifier";

    // smtp
    public static final String SMTP                                      = "/smtp";
    public static final String SMTP_SAVE                                 = "/smtp/save";

    // customers
    public static final String CUSTOMERS                                 = "/customers";
    public static final String CUSTOMERS_DATA                            = "/customers/data";
    public static final String CUSTOMERS_ADD                             = "/customers/add";
    public static final String CUSTOMERS_ADD_SAVE                        = "/customers/add/save";
    public static final String CUSTOMERS_EDIT_SAVE                       = "/customers/edit/save";
    public static final String CUSTOMER_VIEW                             = "/customer/view";
    public static final String CUSTOMER_REMOVE                           = "/customer/remove";
    public static final String CUSTOMER_ADDRESS_SAVE                     = "/customer/address/save";
    public static final String CUSTOMER_ADDRESS_REMOVE                   = "/customer/address/remove";


    // areas
    public static final String AREAS                                     = "/areas";
    public static final String AREA_EDIT                                 = "/areas/edit";
    public static final String AREA_EDIT_SAVE                            = "/areas/edit/save";
    public static final String AREA_REMOVE                               = "/area/remove";

    // categories
    public static final String CATEGORIES                                = "/categories";
    public static final String CATEGORY_EDIT                             = "/category/edit";
    public static final String CATEGORY_EDIT_SAVE                        = "/category/edit/save";
    public static final String CATEGORY_REMOVE                           = "/category/remove";

    // subcategories
    public static final String SUBCATEGORIES                             = "/subcategories";
    public static final String SUBCATEGORY_EDIT                          = "/subcategory/edit";
    public static final String SUBCATEGORY_EDIT_SAVE                     = "/subcategory/edit/save";
    public static final String SUBCATEGORY_REMOVE                        = "/subcategory/remove";

    // vendors
    public static final String VENDORS                                   = "/vendors";
    public static final String VENDORS_ADD                               = "/vendors/add";
    public static final String VENDORS_ADD_SAVE                          = "/vendors/add/save";
    public static final String VENDORS_EDIT_SAVE                         = "/vendors/edit/save";
    public static final String VENDOR_VIEW                               = "/vendor/view";
    public static final String VENDOR_REMOVE                             = "/vendor/remove";

    // label
    public static final String LABELS                                    = "/labels";
    public static final String LABELS_DATA                               = "/labels/data";
    public static final String LABELS_SAVE                               = "/labels/save";
    public static final String LABELS_CLEAR                              = "/labels/clear";


    // posts
    public static final String POSTS                                     = "/posts";
    public static final String POST_EDIT                                 = "/post/edit";
    public static final String POST_EDIT_SAVE                            = "/post/edit/save";
    public static final String POST_REMOVE                               = "/post/remove";
    public static final String POST_IMAGE_SAVE                           = "/post/image/save";


    // searches
    public static final String BE_SEARCHES                               = "/be-searches";

    // eventrequests
    public static final String BE_EVENT_REQUESTS                         = "/be-eventrequests";

    // pagereports
    public static final String BE_PAGE_REPORTS                           = "/be-pagereports";

    // pageclaims
    public static final String BE_PAGE_CLAIMS                            = "/be-pageclaims";

    // eventreports
    public static final String BE_EVENT_REPORTS                           = "/be-eventreports";

    // events
    public static final String BE_EVENTS                                 = "/be-events";
    public static final String BE_EVENTS_DATA                            = "/be-events/data";
    public static final String BE_EVENT_EDIT                             = "/be-event/edit";
    public static final String BE_EVENT_EDIT_SAVE                        = "/be-event/edit/save";
    public static final String BE_EVENT_REMOVE                           = "/be-event/remove";
    public static final String BE_EVENT_REMOVE_MULTIPLE                  = "/be-event/remove/multiple";
    public static final String BE_EVENT_QRCODE_GENERATE                  = "/be-event/qrcode/generate";
    public static final String BE_EVENT_REMOVE_POSTER                    = "/be-event/poster/remove";

    public static final String BE_EVENTS_PROMO_SAVE                      = "/be-events/promo/save";

    // pages
    public static final String BE_PAGES                                 = "/be-pages";
    public static final String BE_PAGES_DATA                            = "/be-pages/data";
    public static final String BE_PAGE_EDIT                             = "/be-page/edit";
    public static final String BE_PAGE_EDIT_SAVE                        = "/be-page/edit/save";
    public static final String BE_PAGE_REMOVE                           = "/be-page/remove";
    public static final String BE_PAGE_QRCODE_GENERATE                  = "/be-page/qrcode/generate";
    public static final String BE_PAGE_REMOVE_POSTER                    = "/be-page/poster/remove";
    public static final String BE_PAGE_REMOVE_PROFILE_IMAGE             = "/be-page/profile/image/remove";

    // home slider
    public static final String HOME_SLIDERS                              = "/home_sliders";
    public static final String HOME_SLIDER_EDIT                          = "/home_slider/edit";
    public static final String HOME_SLIDER_EDIT_SAVE                     = "/home_slider/edit/save";
    public static final String HOME_SLIDER_REMOVE                        = "/home_slider/remove";

    // sponsor events
    public static final String SPONSOR_EVENT_COLLECTION                  = "/sponsor-event-collection";
    public static final String SPONSOR_EVENT_EDIT                        = "/sponsor-event-edit";
    public static final String SPONSOR_EVENT_EDIT_SAVE                   = "/sponsor-event-edit/save";
    public static final String SPONSOR_EVENT_REMOVE                      = "/sponsor-event/remove";

    // sponsor pages
    public static final String SPONSOR_PAGE_COLLECTION                   = "/sponsor-page-collection";
    public static final String SPONSOR_PAGE_EDIT                         = "/sponsor-page-edit";
    public static final String SPONSOR_PAGE_EDIT_SAVE                    = "/sponsor-page-edit/save";
    public static final String SPONSOR_PAGE_REMOVE                       = "/sponsor-page/remove";

    public static final String PAGES_UPLOAD                              = "/be-pages/upload";
    public static final String PAGES_UPLOAD_FOTO                         = "/be-pages/upload/foto";
    public static final String PAGES_UPLOAD_VERIFY                       = "/be-pages/upload/verify";
    public static final String PAGES_UPLOAD_SAVE                         = "/be-pages/upload/save";
    public static final String PAGES_UPLOAD_SAVE_STATUS                  = "/be-pages/upload/save/status";

    public static final String EVENTS_UPLOAD                             = "/be-events/upload";
    public static final String EVENTS_UPLOAD_VERIFY                      = "/be-events/upload/verify";
    public static final String EVENTS_UPLOAD_SAVE                        = "/be-events/upload/save";
    public static final String EVENTS_UPLOAD_SAVE_STATUS                 = "/be-events/upload/save/status";

    public static final String MAILNOTIFICATIONS                         = "/be-mailnotifications";
    public static final String MAILNOTIFICATIONS_DATA_SEND               = "/be-mailnotifications/data/send";
    public static final String MAILNOTIFICATION_STATUS                   = "/be-mailnotification/status";
    public static final String MAILNOTIFICATION_VIEW                     = "/be-mailnotification/view";

    // analysis
    public static final String ANALYSIS_DATA                             = "/analysis/data";
    public static final String ANALYSIS_PIVOT                            = "/analysis/pivot";

    // support
    public static final String IMAGE                                     = "/image";
    public static final String IMAGE_SYSTEM                              = "/imagesystem";
    public static final String THUMBNAIL                                 = "/thumbnail";
    public static final String PRINT                                     = "/print";
    public static final String PRINT_TEMP                                = "/print/temp";
    public static final String PRINT_TEMP_TXT                            = "/print/temptxt";
    public static final String PRINT_TEMP_CSV                            = "/print/tempcsv";
    public static final String FILE                                      = "/file";
    public static final String DOWNLOAD                                  = "/download";

    // geocoder
    public static final String GEOCODER_CLEAR                            = "/geocoder/clear";

    // data
    public static final String DATA_CITIES                               = "/data/cities";
    public static final String DATA_PAGES                                = "/data/pages";
    public static final String DATA_SEARCH_EVENTS                        = "/data/search/events";
    public static final String DATA_SEARCH_EVENTS_CONTAINER              = "/data/search/events/container";
    public static final String DATA_SEARCH                               = "/data/search";
    public static final String DATA_CUSTOMERS                            = "/data/customers";
    public static final String DATA_TAG_PAGE                             = "/data/tag/page";
    public static final String DATA_TAG_EVENT                            = "/data/tag/event";
    public static final String DATA_USER                                 = "/data/user";

    public static final String DATA_CITIES_AC                            = "/data/cities/autocomplete";

    public static final String DATA_CITIES_WITH_PROVINCE                 = "/data/citieswithprovince";
    public static final String DATA_SUBCATEGORIES                        = "/data/subcategories";

    public static final String API_PING                                  = "/be/api/ping";
    public static final String API_PAGES                                 = "/be/api/pages";
    public static final String API_PAGE                                  = "/be/api/page";
    public static final String API_EVENTS                                = "/be/api/events";
    public static final String API_EVENT                                 = "/be/api/event";
    public static final String API_PAGES_OPERATE                         = "/be/api/pages/operate";
    public static final String API_EVENTS_OPERATE                        = "/be/api/events/operate";
    public static final String API_RELOAD_CACHE                          = "/be/api/reload/cache";

    ///////////
    // KEYWORDS
    public static final String IDENTIFIER_SUFFIX                         = "/:identifier";

    public static final String SUCCESS_SUFFIX                            = "/ok";
    public static final String ERROR_SUFFIX                              = "/ko";


    /////////////////////
    // utility methods //
    private static Map<String, String> _paths;

    public static Map<String, String> paths() {
        if (_paths == null) {

            _paths = new HashMap<>();
            for (Field field : Paths.class.getFields()) {
                if (field.getType().isAssignableFrom(String.class)) {

                    String name = field.getName();
                    String path = null;
                    try {
                        path = (String) field.get(null);
                    } catch (IllegalArgumentException | IllegalAccessException ex) {
                        LOGGER.error("suppressed", ex);
                    }

                    // put base value
                    _paths.put(name, path);

                    // put annotated values
                    LocalizedPath[] annotations = field.getDeclaredAnnotationsByType(LocalizedPath.class);
                    if ((annotations != null) &&
                                (annotations.length > 0)) {

                        // add other languages
                        for (LocalizedPath annotation : annotations) {
                            _paths.put(name + "_" + StringUtils.upperCase(annotation.language()), annotation.path());
                        }

                        // this language intentionally differs from the one present
                        // in defaults class... path constants are in english, by design
                        final String language = "en";
                        if (!_paths.containsKey(name + "_" + StringUtils.upperCase(language))) {
                            _paths.put(name + "_" + StringUtils.upperCase(language), path);
                        }

                    }

                }
            }

        }

        return _paths;
    }


    private static Map<String, String> _languages;

    public static Map<String, String> languages() {
        if (_languages == null) {

            _languages = new HashMap<>();

            // put base value
            _languages.put(Defaults.LANGUAGE, "");
            for (Field field : Paths.class.getFields()) {
                if (field.getType().isAssignableFrom(String.class)) {

                    // put annotated values
                    LocalizedPath[] annotations = field.getDeclaredAnnotationsByType(LocalizedPath.class);
                    if ((annotations != null) &&
                                (annotations.length > 0)) {

                        // add other languages
                        for (LocalizedPath annotation : annotations) {
                            _languages.put(annotation.language(), "");
                        }

                    }

                }
            }

        }

        return _languages;
    }

    public static Map<String, String> localizedPathsMap(String path) {
        Map<String, String> map = null;

        // match path name
        String name = null;
        for (String key : paths().keySet()) {
            if (StringUtils.equals(paths().get(key), path)) {
                if (StringUtils.isBlank(name) || (key.length() < StringUtils.length(name))) {
                    name = key;
                }
            }
        }

        // seek base name
        Field base = null;
        if (StringUtils.isNotBlank(name)) {
            for (Field field : Paths.class.getFields()) {
                if (field.getType().isAssignableFrom(String.class)) {
                    if (StringUtils.equals(name, field.getName())) {
                        base = field;
                        break;
                    }
                }
            }
            if (base == null) {
                String langSuffix = StringUtils.substringAfterLast(name, "_");
                if (StringUtils.length(langSuffix) == 2) {
                    name = StringUtils.substringBeforeLast(name, "_");
                    for (Field field : Paths.class.getFields()) {
                        if (field.getType().isAssignableFrom(String.class)) {
                            if (StringUtils.equals(name, field.getName())) {
                                base = field;
                                break;
                            }
                        }
                    }
                }
            }
        } else {
            LOGGER.error("path not found " + path);
        }

        if (base != null) {

            map = new HashMap<>();

            // base path
            String value = null;
            try {
                value = (String) base.get(Paths.class);
            } catch (IllegalArgumentException | IllegalAccessException ex) {
                LOGGER.error("value not found " + base.getName());
            }

            if (StringUtils.isNotBlank(value)) {
                map.put(Defaults.LANGUAGE, RouteUtils.language(value, Defaults.LANGUAGE));
            }

            // localized paths
            LocalizedPath[] annotations = null;
            LocalizedPath[] anns = base.getDeclaredAnnotationsByType(LocalizedPath.class);
            if ((anns != null) &&
                        (anns.length > 0)) {
                annotations = anns;
            }

            if ((annotations != null) &&
                        (annotations.length > 0)) {

                // add other languages
                for (LocalizedPath annotation : annotations) {
                    map.put(annotation.language(), RouteUtils.language(annotation.path(), annotation.language()));
                }

            }

            // filling all languages
            if (map.size() > 0) {
                if (StringUtils.isNotBlank(value)) {
                    for (String language : languages().keySet()) {
                        if (!map.containsKey(language)) {
                            map.put(language, RouteUtils.language(value, language));
                        }
                    }
                }
            }

        } else {
            if (StringUtils.isNotBlank(name)) {
                LOGGER.error("path constant not found " + name);
            }
        }

        return map;
    }

    public static String[] localizedPaths(String path) {
        List<String> paths = null;

        if (StringUtils.isNotBlank(path)) {


            //////////////////////////////////////
            // base path (without language suffix)
            paths = new ArrayList<>();
            paths.add(path);


            /////////////////////////////////////////
            // localized paths (with language suffix)

            // match path name
            String name = null;
            for (String key : paths().keySet()) {
                if (StringUtils.equals(paths().get(key), path)) {
                    if (StringUtils.isBlank(name) || (key.length() < StringUtils.length(name))) {
                        name = key;
                    }
                }
            }

            // localized paths
            LocalizedPath[] annotations = null;
            if (StringUtils.isNotBlank(name)) {
                for (Field field : Paths.class.getFields()) {
                    if (field.getType().isAssignableFrom(String.class)) {
                        if (StringUtils.equals(name, field.getName())) {
                            LocalizedPath[] anns = field.getDeclaredAnnotationsByType(LocalizedPath.class);
                            if ((anns != null) &&
                                        (anns.length > 0)) {
                                annotations = anns;
                            }
                            break;
                        }
                    }
                }
            } else {
                LOGGER.error("path not found " + path);
            }

            if ((annotations != null) &&
                        (annotations.length > 0)) {

                // add other languages
                for (LocalizedPath annotation : annotations) {
                    paths.add(annotation.path());
                }

            }

        } else {
            LOGGER.error("empty path");
        }

        // returning list as array of string
        String[] result = null;
        if ((paths != null) && (!paths.isEmpty())) {
            result = paths.toArray(new String[paths.size()]);
        }

        return result;
    }

    public static String back(String backUrl) {
        return StringUtils.isNotBlank(backUrl) ? ("?backUrl=" + backUrl) : "";
    }

    public static String success(String path) {
        return path + SUCCESS_SUFFIX;
    }

    public static String error(String path) {
        return path + ERROR_SUFFIX;
    }

    public static boolean hasSuccess(Request request) {
        // a route ending with "/ok" means that a confirmation message should be shown
        return (request != null) ? StringUtils.containsIgnoreCase(request.pathInfo(), SUCCESS_SUFFIX) : false;
    }

    public static boolean hasError(Request request) {
        // a route ending with "/ko" means that an error message should be shown
        return (request != null) ? StringUtils.containsIgnoreCase(request.pathInfo(), ERROR_SUFFIX) : false;
    }

}
