/**
 * Content Truncation Utility
 * Provides HTML-safe truncation functionality for page descriptions
 * 
 * <AUTHOR> Agent
 * @version 1.0.0
 */

(function(window) {
    'use strict';

    /**
     * HTML-safe content truncation utility
     */
    var ContentTruncation = {
        
        /**
         * Truncates HTML content safely by counting visible characters
         * while preserving HTML tag integrity
         * 
         * @param {string} htmlContent - The HTML content to truncate
         * @param {number} maxLength - Maximum number of visible characters
         * @returns {object} Object with truncated content and metadata
         */
        truncateHtml: function(htmlContent, maxLength) {
            // Input validation
            if (!htmlContent || typeof htmlContent !== 'string') {
                return {
                    truncated: '',
                    remaining: '',
                    isTruncated: false,
                    visibleLength: 0
                };
            }

            if (maxLength <= 0 || !isFinite(maxLength)) {
                return {
                    truncated: htmlContent,
                    remaining: '',
                    isTruncated: false,
                    visibleLength: this.getVisibleCharCount(htmlContent)
                };
            }

            try {
                // Create a temporary DOM element to parse HTML safely
                var tempDiv = document.createElement('div');
                tempDiv.innerHTML = htmlContent;

                var result = this._processNode(tempDiv, maxLength);

                return {
                    truncated: result.truncatedHtml || '',
                    remaining: result.remainingHtml || '',
                    isTruncated: result.charCount >= maxLength,
                    visibleLength: result.charCount || 0
                };
            } catch (error) {
                console.error('Error in HTML truncation:', error);
                // Fallback to simple text truncation
                var textContent = htmlContent.replace(/<[^>]*>/g, '');
                if (textContent.length <= maxLength) {
                    return {
                        truncated: htmlContent,
                        remaining: '',
                        isTruncated: false,
                        visibleLength: textContent.length
                    };
                } else {
                    return {
                        truncated: textContent.substring(0, maxLength),
                        remaining: textContent.substring(maxLength),
                        isTruncated: true,
                        visibleLength: maxLength
                    };
                }
            }
        },

        /**
         * Recursively processes DOM nodes to truncate content
         * 
         * @private
         * @param {Node} node - DOM node to process
         * @param {number} maxLength - Maximum character count
         * @param {number} currentCount - Current character count
         * @returns {object} Processing result
         */
        _processNode: function(node, maxLength, currentCount) {
            currentCount = currentCount || 0;
            var truncatedHtml = '';
            var remainingHtml = '';
            var charCount = currentCount;
            var truncationPoint = false;

            for (var i = 0; i < node.childNodes.length; i++) {
                var child = node.childNodes[i];
                
                if (charCount >= maxLength && !truncationPoint) {
                    truncationPoint = true;
                }

                if (child.nodeType === Node.TEXT_NODE) {
                    var text = child.textContent;
                    var remainingChars = maxLength - charCount;
                    
                    if (!truncationPoint && remainingChars > 0) {
                        if (text.length <= remainingChars) {
                            truncatedHtml += text;
                            charCount += text.length;
                        } else {
                            // Find a good breaking point (space, punctuation)
                            var truncateAt = this._findBreakPoint(text, remainingChars);
                            truncatedHtml += text.substring(0, truncateAt);
                            remainingHtml += text.substring(truncateAt);
                            charCount += truncateAt;
                            if (charCount < maxLength) {
                                charCount = maxLength;
                            }
                            truncationPoint = true;
                        }
                    } else {
                        remainingHtml += text;
                    }
                } else if (child.nodeType === Node.ELEMENT_NODE) {
                    var tagName = child.tagName.toLowerCase();
                    var attributes = this._getAttributesString(child);
                    if (tagName === 'br') {
                        truncatedHtml += '<br/>';
                        continue;
                    }
                    
                    if (!truncationPoint) {
                        var childResult = this._processNode(child, maxLength, charCount);

                        if (childResult.charCount < maxLength) {
                            truncatedHtml += '<' + tagName + attributes + '>' + 
                                           childResult.truncatedHtml + 
                                           '</' + tagName + '>';
                            charCount = childResult.charCount;
                            
                            if (childResult.remainingHtml) {
                                remainingHtml += '<' + tagName + attributes + '>' + 
                                               childResult.remainingHtml + 
                                               '</' + tagName + '>';
                                truncationPoint = true;
                            }
                        } else {
                            if (childResult.truncatedHtml) {
                                truncatedHtml += '<' + tagName + attributes + '>' + 
                                               childResult.truncatedHtml + 
                                               '</' + tagName + '>';
                            }
                            remainingHtml += '<' + tagName + attributes + '>' + 
                                           childResult.remainingHtml + 
                                           '</' + tagName + '>';
                            charCount = childResult.charCount;
                            truncationPoint = true;
                        }
                    } else {
                        remainingHtml += child.outerHTML;
                    }
                }

                console.warn(truncatedHtml);
            }

            return {
                truncatedHtml: truncatedHtml,
                remainingHtml: remainingHtml,
                charCount: charCount
            };
        },

        /**
         * Finds a good breaking point in text (preferring spaces and punctuation)
         * 
         * @private
         * @param {string} text - Text to find break point in
         * @param {number} maxLength - Maximum length to consider
         * @returns {number} Index of break point
         */
        _findBreakPoint: function(text, maxLength) {
            if (maxLength >= text.length) {
                return text.length;
            }

            // Look for space or punctuation within the last 20% of allowed length
            var searchStart = Math.max(0, maxLength - Math.floor(maxLength * 0.2));
            var breakChars = [' ', '.', ',', ';', ':', '!', '?', '\n', '\t'];
            
            for (var i = maxLength - 1; i >= searchStart; i--) {
                if (breakChars.indexOf(text.charAt(i)) !== -1) {
                    return i + 1; // Include the break character
                }
            }
            
            // If no good break point found, truncate at maxLength
            return maxLength;
        },

        /**
         * Gets attributes string from an element
         * 
         * @private
         * @param {Element} element - DOM element
         * @returns {string} Attributes string
         */
        _getAttributesString: function(element) {
            var attrs = '';
            for (var i = 0; i < element.attributes.length; i++) {
                var attr = element.attributes[i];
                attrs += ' ' + attr.name + '="' + attr.value + '"';
            }
            return attrs;
        },

        /**
         * Estimates visible character count in HTML content
         * 
         * @param {string} htmlContent - HTML content to analyze
         * @returns {number} Estimated visible character count
         */
        getVisibleCharCount: function(htmlContent) {
            if (!htmlContent) return 0;
            
            var tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;
            return tempDiv.textContent.length;
        }
    };

    // Export to global scope
    window.ContentTruncation = ContentTruncation;

})(window);
