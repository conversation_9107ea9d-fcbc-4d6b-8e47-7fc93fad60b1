var ias;
var loading = false;
var limit = parseInt($('#pagination').text(), 0);
var skip = 0;

(function () {
    bindIas();
    binPageFollowAndNotify();
    bindPageFollow();
    bindPageNotify();
    bindRequestEvent();
    bindRequestEventRemove();
    bindReportPage();
    bindPageMerge();
    bindPageRelease();
})();

function binPageFollowAndNotify() {
    $('.page-add-follow-and-add-notification').off();
    $('.page-add-follow-and-add-notification').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#pageFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }

        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }

        if (event.target.getAttribute('data-reload-id')) {
            var idToReload = "#" + event.target.getAttribute('data-reload-id');
        }

        if (user === 'unlogged') {

            // warn
            Swal.fire({
                position: 'top',
                icon: 'error',
                title: 'Oh oh! :(',
                text: label('common.must.be.registered.page'),
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary btn-lg',
                },
                confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                // todo
                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:">' + label('common.contact.assistance') + '</a>'
            });


            return false;
        }

        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);


        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success: function (returndata) {
                event.preventDefault();

                // prepare call
                var url = new URI($('#pageNotificationToggleUri').attr('href'));
                if (!url) {
                    console.error('missing url');
                    return false;
                }

                var user = event.target.getAttribute('data-user');
                if (!user) {
                    user = event.target.parentNode.getAttribute('data-user');
                }
                if (!user) {
                    console.error('missing user');
                    return false;
                }

                var pageId = event.target.getAttribute('data-page-id');
                if (!pageId) {
                    pageId = event.target.parentNode.getAttribute('data-page-id');
                }
                if (!pageId) {
                    console.error('missing pageId');
                    return false;
                }

                if (event.target.getAttribute('data-reload-id')) {
                    var idToReload = "#" + event.target.getAttribute('data-reload-id');
                }

                if (user === 'unlogged') {

                    // warn
                    Swal.fire({
                        position: 'top',
                        icon: 'error',
                        title: 'Oh oh! :(',
                        text: label('common.must.be.registered.request.notification'),
                        buttonsStyling: false,
                        customClass: {
                            confirmButton: 'btn btn-primary btn-lg',
                        },
                        confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                        // todo
                        footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:">' + label('common.contact.assistance') + '</a>'
                    });


                    return false;
                }

                url.removeSearch("pageId");
                url.addSearch("pageId", pageId);

                $.blockUI();
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: null,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (returndata) {
                        $.unblockUI();

                        var msg = label('common.page.follow.added');

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function () {
                            var url = new URI();
                            if (idToReload) {
                                $(idToReload).load(url + ' ' + idToReload + ' > *', function () {
                                    binPageFollowAndNotify();
                                    bindPageFollow();
                                    bindPageNotify();
                                    bindRequestEvent();
                                    bindRequestEventRemove();
                                    bindReportPage();
                                });
                            } else {
                                $('#panelFollower').load(url + ' #panelFollower > *', function () {
                                    binPageFollowAndNotify();
                                    bindPageFollow();
                                    bindPageNotify();
                                    bindRequestEvent();
                                    bindRequestEventRemove();
                                    bindReportPage();
                                });
                            }
                        });

                    },
                    error: function (response, status, errorThrown) {
                        $.unblockUI();

                        // warn
                        $.alert({
                            theme: 'supervan',
                            escapeKey: true,
                            animation: 'top',
                            closeAnimation: 'bottom',
                            backgroundDismiss: true,
                            title: 'Oh oh! :(',
                            content: label('common.add.failed')
                        });
                    }
                });
            },
            error: function (response, status, errorThrown) {
                $.unblockUI();

                // warn
                $.alert({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: 'Oh oh! :(',
                    content: label('common.add.failed')
                });
            }
        });
        return false;
    });
    
    $('.page-remove-follow-and-remove-notification').off();
    $('.page-remove-follow-and-remove-notification').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#pageFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }

        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }

        if (event.target.getAttribute('data-reload-id')) {
            var idToReload = "#" + event.target.getAttribute('data-reload-id');
        }

        if (user === 'unlogged') {

            // warn
            Swal.fire({
                position: 'top',
                icon: 'error',
                title: 'Oh oh! :(',
                text: label('common.must.be.registered.page'),
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary btn-lg',
                },
                confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                // todo
                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:">' + label('common.contact.assistance') + '</a>'
            });


            return false;
        }

        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);


        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success: function (returndata) {
                event.preventDefault();

                // prepare call
                var url = new URI($('#pageNotificationToggleUri').attr('href'));
                if (!url) {
                    console.error('missing url');
                    return false;
                }

                var user = event.target.getAttribute('data-user');
                if (!user) {
                    user = event.target.parentNode.getAttribute('data-user');
                }
                if (!user) {
                    console.error('missing user');
                    return false;
                }

                var pageId = event.target.getAttribute('data-page-id');
                if (!pageId) {
                    pageId = event.target.parentNode.getAttribute('data-page-id');
                }
                if (!pageId) {
                    console.error('missing pageId');
                    return false;
                }

                if (event.target.getAttribute('data-reload-id')) {
                    var idToReload = "#" + event.target.getAttribute('data-reload-id');
                }

                if (user === 'unlogged') {

                    // warn
                    Swal.fire({
                        position: 'top',
                        icon: 'error',
                        title: 'Oh oh! :(',
                        text: label('common.must.be.registered.request.notification'),
                        buttonsStyling: false,
                        customClass: {
                            confirmButton: 'btn btn-primary btn-lg',
                        },
                        confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                        // todo
                        footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:">' + label('common.contact.assistance') + '</a>'
                    });


                    return false;
                }

                url.removeSearch("pageId");
                url.addSearch("pageId", pageId);

                $.blockUI();
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: null,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (returndata) {
                        $.unblockUI();

                        var msg = label('common.page.follow.removed');

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function () {
                            var url = new URI();
                            if (idToReload) {
                                $(idToReload).load(url + ' ' + idToReload + ' > *', function () {
                                    binPageFollowAndNotify();
                                    bindPageFollow();
                                    bindPageNotify();
                                    bindRequestEvent();
                                    bindRequestEventRemove();
                                    bindReportPage();
                                });
                            } else {
                                $('#panelFollower').load(url + ' #panelFollower > *', function () {
                                    binPageFollowAndNotify();
                                    bindPageFollow();
                                    bindPageNotify();
                                    bindRequestEvent();
                                    bindRequestEventRemove();
                                    bindReportPage();
                                });
                            }
                        });

                    },
                    error: function (response, status, errorThrown) {
                        $.unblockUI();

                        // warn
                        $.alert({
                            theme: 'supervan',
                            escapeKey: true,
                            animation: 'top',
                            closeAnimation: 'bottom',
                            backgroundDismiss: true,
                            title: 'Oh oh! :(',
                            content: label('common.add.failed')
                        });
                    }
                });
            },
            error: function (response, status, errorThrown) {
                $.unblockUI();

                // warn
                $.alert({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: 'Oh oh! :(',
                    content: label('common.add.failed')
                });
            }
        });
        return false;
    });
}

function bindPageFollow() {
    $('.page-add-follow').off();
    $('.page-add-follow').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#pageFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }

        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }

        var value = event.target.getAttribute('data-value');
        if (!value) {
            value = event.target.parentNode.getAttribute('data-value');
        }
        
        var idToReload;
        if (event.target.getAttribute('data-reload-id')) {
            idToReload = "#" + event.target.getAttribute('data-reload-id');
        } else {
            idToReload = "#" + $(event.target).parent().attr('data-reload-id');
        }

        if (user === 'unlogged') {

            // warn
            Swal.fire({
                position: 'top',
                icon: 'error',
                title: 'Oh oh! :(',
                text: label('common.must.be.registered.page'),
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary btn-lg',
                },
                confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                // todo
                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
            });


            return false;
        }

        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);


        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();

                        var msg = label('common.page.follow.added');
                        if (value === 'active') {
                            var msg = label('common.page.follow.removed');
                        }

                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function () {
                            var url = new URI();
                            if (idToReload) {
                                $(idToReload).load(url + ' ' + idToReload + ' > *', function() {
                                    binPageFollowAndNotify();
                                    bindPageFollow();
                                    bindPageNotify();
                                    bindRequestEvent();
                                    bindRequestEventRemove();
                                    bindReportPage();
                                });
                            } else {
                                $('#panelFollower').load(url + ' #panelFollower > *', function () {
                                    binPageFollowAndNotify();
                                    bindPageFollow();
                                    bindPageNotify();
                                    bindRequestEvent();
                                    bindRequestEventRemove();
                                    bindReportPage();
                                });
                            }
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();

                        // warn
                        $.alert({
                            theme: 'supervan',
                            escapeKey: true,
                            animation: 'top',
                            closeAnimation: 'bottom',
                            backgroundDismiss: true,
                            title: 'Oh oh! :(',
                            content: label('common.add.failed')
                        });
                    }
        });

        return false;
    });
}

function bindPageNotify() {
    $('.page-add-notification').off();
    $('.page-add-notification').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#pageNotificationToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }

        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }

        var value = event.target.getAttribute('data-value');
        if (!value) {
            value = event.target.parentNode.getAttribute('data-value');
        }
        
        var idToReload;
        if (event.target.getAttribute('data-reload-id')) {
            idToReload = "#" + event.target.getAttribute('data-reload-id');
        } else {
            idToReload = "#" + $(event.target).parent().attr('data-reload-id');
        }

        if (user === 'unlogged') {

            // warn
            Swal.fire({
                position: 'top',
                icon: 'error',
                title: 'Oh oh! :(',
                text: label('common.must.be.registered.request.notification'),
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary btn-lg',
                },
                confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                // todo
                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
            });


            return false;
        }

        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);


        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();

                        var msg = label('common.page.notify.added');
                        if (value === 'active') {
                            var msg = label('common.page.notify.removed');
                        }

                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function () {
                            var url = new URI();
                            if (idToReload) {
                                $(idToReload).load(url + ' ' + idToReload + ' > *', function() {
                                    binPageFollowAndNotify();
                                    bindPageFollow();
                                    bindPageNotify();
                                    bindRequestEvent();
                                    bindRequestEventRemove();
                                    bindReportPage();
                                });
                            } else {
                                $('#panelFollower').load(url + ' #panelFollower > *', function () {
                                    binPageFollowAndNotify();
                                    bindPageFollow();
                                    bindPageNotify();
                                    bindRequestEvent();
                                    bindRequestEventRemove();
                                    bindReportPage();
                                });
                            }
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();

                        // warn
                        $.alert({
                            theme: 'supervan',
                            escapeKey: true,
                            animation: 'top',
                            closeAnimation: 'bottom',
                            backgroundDismiss: true,
                            title: 'Oh oh! :(',
                            content: label('common.add.failed')
                        });
                    }
        });

        return false;
    });
}

function bindRequestEvent() {
    $('.request-event-btn').off();
    $('.request-event-btn').click(function (event) {
        event.preventDefault();

        if ($("#request-event").valid()) {

            // prepare call
            var actionURL = $('#request-event').attr('action');
            var url = new URI(actionURL);
            if (!url) {
                console.error('missing url');
                return false;
            }

            var user = event.target.getAttribute('data-user');
            if (!user) {
                user = event.target.parentNode.getAttribute('data-user');
            }
            if (!user) {
                console.error('missing user');
                return false;
            }

            if (user === 'unlogged') {

                // warn
                Swal.fire({
                    position: 'top',
                    icon: 'error',
                    title: 'Oh oh! :(',
                    text: label('common.must.be.registered.request.event'),
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'btn btn-primary btn-lg',
                    },
                    confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                    // todo
                    footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                });


                return false;
            }

            var formData = new FormData($('#request-event')[0]);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();

                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: 'Evento richiesto',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(function () {
                                window.location.reload();
                            });

                        },
                error:
                        function (response, status, errorThrown) {
                            $.unblockUI();

                            // warn
                            Swal.fire({
                                position: 'top',
                                icon: 'error',
                                title: 'Oh oh! :(',
                                text: label('common.operation.error'),
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: label('common.continue'),
                                // todo
                                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                            });
                        }
            });

            return false;
        }
    });
}

function bindRequestEventRemove() {
    $('.page-remove-request').off();
    $('.page-remove-request').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#pageRemoveRequestUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }

        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);

        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: label('event.request.remove'),
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function () {
                            window.location.reload();
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();

                        // warn
                        Swal.fire({
                            position: 'top',
                            icon: 'error',
                            title: 'Oh oh! :(',
                            text: label('common.operation.error'),
                            buttonsStyling: false,
                            customClass: {
                                confirmButton: 'btn btn-primary btn-lg',
                            },
                            confirmButtonText: label('common.continue'),
                            // todo
                            footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                        });
                    }
        });

        return false;

    });
}

function bindReportPage() {
    $('.report-page-btn').off();
    $('.report-page-btn').click(function (event) {
        event.preventDefault();

        if ($("#report-page").valid()) {

            // prepare call
            var actionURL = $('#report-page').attr('action');
            var url = new URI(actionURL);
            if (!url) {
                console.error('missing url');
                return false;
            }

            var user = event.target.getAttribute('data-user');
            if (!user) {
                user = event.target.parentNode.getAttribute('data-user');
            }
            if (!user) {
                console.error('missing user');
                return false;
            }

            if (user === 'unlogged') {

                // warn
                Swal.fire({
                    position: 'top',
                    icon: 'error',
                    title: 'Oh oh! :(',
                    text: label('common.must.be.registered.report.page'),
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'btn btn-primary btn-lg',
                    },
                    confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                    // todo
                    footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                });


                return false;
            }

            var formData = new FormData($('#report-page')[0]);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();

                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: label('event.requested'),
                                showConfirmButton: false,
                                timer: 1500
                            }).then(function () {
                                window.location.reload();
                            });

                        },
                error:
                        function (response, status, errorThrown) {
                            $.unblockUI();

                            // warn
                            Swal.fire({
                                position: 'top',
                                icon: 'error',
                                title: 'Oh oh! :(',
                                text: label('common.operation.error'),
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: label('common.continue'),
                                // todo
                                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                            });
                        }
            });

            return false;
        }
    });
}

function bindPageMerge() {
    // Inizializza Select2
    var initials = [];
    $('#modalPageMergePageId').select2({
        maximumSelectionLength: 1,
        minimumInputLength: 3,
        data: initials,
        ajax: {
            url: $('#dataPagesUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    name: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                var results = [];
                if (data) {
                    results = data.map(function (item) {
                        return {
                            id: item[0],
                            text: item[1],
                            avatar: item[2],
                            descr: item[4]
                        };
                    });
                }

                return {
                    results: results,
                    pagination: {
                        more: data && data[0] && data[0][5] ? (params.page * 10) < data[0][5] : false
                    }
                };
            },
            cache: true
        },
        templateResult: formatRepo,
        templateSelection: formatRepoSelection,
        escapeMarkup: function (markup) {
            return markup;
        }
    });

    function formatRepo(repo) {
        $("#select2-modalPageMergePageId-results").parent().parent().css("z-index", 9999);

        if (repo.loading) {
            return repo.text;
        }

        // Se è un'opzione "Crea"
        if (repo.isCreate || repo.isNew) {
            return $(
                '<div class="select2-result-repository clearfix d-flex align-items-center create-option">' +
                '<div class="select2-result-repository__avatar"><i class="fa-2x bi bi-file-plus"></i></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">Crea "' + repo.text + '"</div>' +
                '</div>' +
                '</div>'
            );
        } else {
            var imgUrl = "https://agor.app/fe/images/avatar/placeholder.jpg";
            if (repo.avatar) {
                imgUrl = $('#imageSearchUri').attr('href') + repo.avatar;
            }

            var $container = $(
                '<div class="select2-result-repository clearfix d-flex align-items-center">' +
                '<div class="select2-result-repository__avatar"><img src="' + imgUrl + '" /></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">' + repo.text + '</div>' +
                "<div class='select2-result-repository__description' style='font-size: small; color: gray;'></div>" +
                '</div>' +
                '</div>'
            );

            if (repo.descr) {
                var shortDesc = repo.descr.length > 50 ? repo.descr.substring(0, 50) + "..." : repo.descr;
                $container.find(".select2-result-repository__description").text(shortDesc);
            }
            return $container;

        }

    }

    function formatRepoSelection(repo) {
        return repo.text;
    }

    $('.btn-merge').off();
    $('.btn-merge').click(function (event) {
        event.preventDefault();

        var firstPageId = $("#currentPageId").text();
        var secondPageId = $("#modalPageMergePageId").val();
        if (secondPageId) {
            secondPageId = secondPageId[0];
        }

        if (firstPageId && secondPageId) {
            location.href = $('#mergePageUri').attr('href') + "?firstPageId=" + firstPageId + "&secondPageId=" + secondPageId;
        } else {
            console.error('missing pageId');
        }
    });
}

function bindIas() {

    var eventElements = document.getElementsByClassName("oneEvent");
    var spinner = document.getElementsByClassName("spinner");
    var containerEvents = $(".containerEvents");
    if (containerEvents.is(":visible")) {
        if (eventElements.length > 0) {
            ias = new InfiniteAjaxScroll('.containerEvents', {
                item: '.oneEvent',
                next: '.pager__next',
                prev: '.pager__prev',
                pagination: '.pager',
                spinner: {
                    // element to show as spinner
                    element: '.spinner',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 300,

                    // this function is called when the button has to be shown
                    show: function (element) {
                        element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function (element) {
                        element.style.opacity = '0'; // default behaviour
                    }
                }
            });


            ias.on('page', (e) => {
                let contEv = $(".containerEvents");
                if (contEv.is(":visible")) {
                    document.title = e.title;
                    let state = history.state;
                    history.replaceState(state, e.title, e.url);
                }
            });

            // disable cache busting
            ias.on('load', function (event) {
                event.nocache = true;
            });
        }
    }
}

function bindPageRelease() {
    $('.page-release-btn').off();
    $('.page-release-btn').click(function (event) {
        event.preventDefault();

        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }

        // Show confirmation dialog
        Swal.fire({
            title: label('page.release.confirm.title'),
            text: label('page.release.confirm.message'),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: label('common.confirm'),
            cancelButtonText: label('common.cancel')
        }).then((result) => {
            if (result.isConfirmed) {
                // Proceed with release
                var url = $('#pageReleaseUri').attr('href');
                if (!url) {
                    console.error('missing release url');
                    return false;
                }

                var data = new FormData();
                data.append('pageId', pageId);

                $.blockUI();
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: data,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (returndata) {
                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: label('page.release.success'),
                            showConfirmButton: false,
                            timer: 2000
                        }).then(function() {
                            window.location.reload();
                        });
                    },
                    error: function (response, status, errorThrown) {
                        $.unblockUI();

                        Swal.fire({
                            position: 'top',
                            icon: 'error',
                            title: 'Error',
                            text: label('page.release.error'),
                            buttonsStyling: false,
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            }
                        });
                    }
                });
            }
        });

        return false;
    });
}
