window.addEventListener("pageshow", function (evt) {
    if (evt.persisted) {
        if ($.cookie("agoraScrollPosition_mypages")) {
            var dataPageBack = $.cookie("agoraScrollPosition_mypages")
            const elementToScroll = document.querySelector("[data-mypages-back=" + dataPageBack + "]");
            if (elementToScroll) {
                elementToScroll.scrollIntoView({block: 'center'});
            }
            $.removeCookie("agoraScrollPosition_mypages");
        }
    }
}, false);
if ('scrollRestoration' in history) {
    // Back off, browser, I got this...
    history.scrollRestoration = 'manual';
}
var ias, iasFollow, iasNotification;
var loading = false;
var limit = parseInt($('#pagination').text(), 0);
var skip = 0;

var loadingPagesFollow = false;
var limitPagesFollow = parseInt($('#paginationPagesFollow').text(), 0);
var skipPagesFollow = 0;

var loadingPagesNotification = false;
var limitPagesNotification = parseInt($('#paginationPagesNotification').text(), 0);
var skipPagesNotification = 0;


$(document).ready(function() {
    
    if ($.cookie("agoraScrollPosition_mypages")) {
        var dataPageBack = $.cookie("agoraScrollPosition_mypages")
        const elementToScroll = document.querySelector("[data-mypages-back=" + dataPageBack + "]");
        if (elementToScroll) {
            setTimeout(function () {
                elementToScroll.scrollIntoView({block: 'center'});
                bindPageLink("agora", null);
            }, 1000);
        }
        $.removeCookie("agoraScrollPosition_mypages");
    }

    bindIas();
    
    bindPageFollow();
    bindPageRemove();
    bindPageRemoveNotification();
    bindTabChange();
    bindPageSetPrimary();
    initializeTooltips();
    
});

function bindPageFollow() {
    $('.page-add-follow').off();
    $('.page-add-follow').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#pageFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }
        
        var value = event.target.getAttribute('data-value');
        if (!value) {
            value = event.target.parentNode.getAttribute('data-value');
        }
        if (!value) {
            console.error('missing value');
            return false;
        }
        
        if (user === 'unlogged') {
            
            // warn
            Swal.fire({
                position: 'top',
                icon: 'error',
                title: 'Oh oh! :(',
                text: label('common.must.be.registered.page'),
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary btn-lg',
                },
                confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                // todo
                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
            });

            
            return false;
        }
        
        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);

        
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();

                        var msg = label('common.page.follow.added');
                        if (value === 'active') {
                            var msg = label('common.page.follow.removed');
                        }
                       
                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function(){
                            var url = new URI();
                            $('#myTabContent').load(url + ' #myTabContent > *', function() {
                                bindPageFollow();
                                bindPageRemove();
                                bindPageRemoveNotification();
                                bindIas();
                                bindTabChange();
                            });
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();

                        // warn
                        $.alert({
                            theme: 'supervan',
                            escapeKey: true,
                            animation: 'top',
                            closeAnimation: 'bottom',
                            backgroundDismiss: true,
                            title: 'Oh oh! :(',
                            content: label('common.add.failed')
                        });
                    }
        });                    

        return false;
    });
}
function bindPageRemove() {
    $('.page-remove').off();
    $('.page-remove').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#pageRemoveUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }
        
        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);

        Swal.fire({
            position: 'center',
            icon: 'warning',
            title: label('pages.delete.page'),
            showConfirmButton: true,
            confirmButtonText: label('common.confirm'),            
            showCancelButton: true,
            cancelButtonText: label('common.cancel'),                        
            buttonsStyling: false,
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-default'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.blockUI();
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: null,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success:
                            function (returndata) {
                                $.unblockUI();

                                var msg = label('pages.page.removed');

                                $.unblockUI();

                                Swal.fire({
                                    position: 'center',
                                    icon: 'success',
                                    title: msg,
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function(){
                                    var url = new URI();
                                    $('#myTabContent').load(url + ' #myTabContent > *', function() {
                                        bindPageFollow();
                                        bindPageRemove();
                                        bindPageRemoveNotification();
                                        bindIas();
                                        bindTabChange();
                                    });
                                });

                            },
                    error:
                            function (response, status, errorThrown) {
                                $.unblockUI();

                                // warn
                                $.alert({
                                    theme: 'supervan',
                                    escapeKey: true,
                                    animation: 'top',
                                    closeAnimation: 'bottom',
                                    backgroundDismiss: true,
                                    title: 'Oh oh! :(',
                                    content: label('common.remove.failed')
                                });
                            }
                });                    

                return false;
            }
        })
    });

}

function bindPageRemoveNotification() {
    $('.page-remove-notification').off();
    $('.page-remove-notification').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#pageNotificationToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }
        
        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);

        Swal.fire({
            position: 'center',
            icon: 'warning',
            title: label('notification.delete.notification'),
            showConfirmButton: true,
            confirmButtonText: label('common.confirm'),            
            showCancelButton: true,
            cancelButtonText: label('common.cancel'),                        
            buttonsStyling: false,
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-default'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.blockUI();
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: null,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success:
                            function (returndata) {
                                $.unblockUI();

                                var msg = 'Notifica rimossa!';

                                $.unblockUI();

                                Swal.fire({
                                    position: 'center',
                                    icon: 'success',
                                    title: msg,
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function(){
                                    var url = new URI();
                                    $('#myTabContent').load(url + ' #myTabContent > *', function() {
                                        bindPageFollow();
                                        bindPageRemove();
                                        bindPageRemoveNotification();
                                        bindPageSetPrimary();
                                        bindIas();
                                        bindTabChange();
                                        initializeTooltips();
                                    });
                                });

                            },
                    error:
                            function (response, status, errorThrown) {
                                $.unblockUI();

                                // warn
                                $.alert({
                                    theme: 'supervan',
                                    escapeKey: true,
                                    animation: 'top',
                                    closeAnimation: 'bottom',
                                    backgroundDismiss: true,
                                    title: 'Oh oh! :(',
                                    content: label('common.remove.failed')
                                });
                            }
                });                    

                return false;
            }
        })
    });

}

function bindPageSetPrimary() {
    $('.page-set-primary').off();

    // Initialize tooltips for primary page buttons
    $('.page-set-primary').each(function() {
        var $this = $(this);
        var isPrimary = $this.data('is-primary') === true;
        var tooltipText = isPrimary ? label('page.primary.page.tooltip') : label('page.set.primary.tooltip');
        $this.attr('title', tooltipText);
        $this.attr('data-bs-original-title', tooltipText);
    });

    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('.page-set-primary[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    $('.page-set-primary').click(function (event) {
        event.preventDefault();

        var pageId = $(this).data('page-id');
        /*var isPrimary = $(this).data('is-primary');*/

        /*if (isPrimary) {
            // Already primary, do nothing
            return false;
        }*/

        // prepare call
        var url = new URI($('#accountPageSetPrimaryUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);

        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success: function (returndata) {
                $.unblockUI();

                var title = label('page.set.primary.success');
                if (returndata === "ok-removed") {
                    label('page.set.primary.success.removed');
                }
                Swal.fire({
                    position: 'center',
                    icon: 'success',
                    title: title,
                    showConfirmButton: false,
                    timer: 1500
                }).then(function(){
                    // Reload the current tab content
                    var url = new URI();
                    $('#myTabContent').load(url + ' #myTabContent > *', function() {
                        bindPageFollow();
                        bindPageRemove();
                        bindPageRemoveNotification();
                        bindPageSetPrimary();
                        bindIas();
                        bindTabChange();
                        initializeTooltips();
                    });
                });
            },
            error: function (response, status, errorThrown) {
                $.unblockUI();

                // warn
                $.alert({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: 'Oh oh! :(',
                    content: label('page.set.primary.error')
                });
            }
        });

        return false;
    });
}

function initializeTooltips() {
    // Initialize all Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function bindPageLink(title, url) {
    $('.pagelink').off();
    $('.pagelink').click(function (event) {
        let state = history.state;
        if (url) {
            history.replaceState(state, title, url);
        } else {
            history.replaceState(state, title, window.location.href);
        }
        $.cookie("agoraScrollPosition_mypages", $(this).attr("data-mypages-back"), {expires: 1});
    });
}

function bindIas() {

    var pageElements = document.getElementsByClassName("myPages");
    var spinner = document.getElementsByClassName("spinner");
    var containerMyPages = $(".containerMyPages");
    var containerPagesFollow = $(".containerPagesFollow");
    var containerMyNotifications = $(".containerMyNotifications");
    if (containerMyPages.is(":visible")) {
        resetIas(); // Resetta ias
        if (pageElements.length > 0) {
            ias = new InfiniteAjaxScroll('.containerMyPages', {
                item: '.myPages',
                next: '.pager__next',
                prev: '.pager__prev',
                pagination: '.pager',
                spinner: {
                    // element to show as spinner
                    element: '.spinner',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 300,

                    // this function is called when the button has to be shown
                    show: function (element) {
                        element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function (element) {
                        element.style.opacity = '0'; // default behaviour
                    }
                }
            });


            ias.on('page', (e) => {
                let contEv = $(".containerMyPages");
                if (contEv.is(":visible")) {
                    document.title = e.title;
                    let state = history.state;
                    history.replaceState(state, e.title, e.url);
                    bindPageLink(e.title, e.url);
                    bindPageFollow();
                    bindPageRemove();
                    bindPageRemoveNotification();
                    bindPageSetPrimary();
                    initializeTooltips();
                }
            });

            // disable cache busting
            ias.on('load', function (event) {
                event.nocache = true;
            });
        } else {
            bindPageFollow();
            bindPageRemove();
            bindPageRemoveNotification();
            bindPageSetPrimary();
            initializeTooltips();
        }
    } else if (containerPagesFollow.is(":visible")) {
        resetIas(); // Resetta ias
        var pageElementsFollow = document.getElementsByClassName("pagesFollow");
        var spinnerFollow = document.getElementsByClassName("spinnerFollow");
        if (pageElementsFollow.length > 0 && spinnerFollow.length > 0) {
            iasFollow = new InfiniteAjaxScroll('.containerPagesFollow', {
                item: '.pagesFollow',
                next: '.pager__nextFollow',
                prev: '.pager__prevFollow',
                pagination: '.pagerFollow',
                spinner: {
                    // element to show as spinner
                    element: '.spinnerFollow',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 600,

                    // this function is called when the button has to be shown
                    show: function (element) {
                        element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function (element) {
                        element.style.opacity = '0'; // default behaviour
                    }
                }
            });


            iasFollow.on('page', (e) => {
                let contEv = $(".containerPagesFollow");
                if (contEv.is(":visible")) {
                    document.title = e.title;
                    let state = history.state;
                    history.replaceState(state, e.title, e.url);
                    bindPageLink(e.title, e.url);
                    bindPageFollow();
                    bindPageRemove();
                    bindPageRemoveNotification();
                }
            });

            // disable cache busting
            iasFollow.on('load', function (event) {
                event.nocache = true;
            });
        } else {
            bindPageFollow();
            bindPageRemove();
            bindPageRemoveNotification();
            bindPageSetPrimary();
            initializeTooltips();
        }
    } else if (containerMyNotifications.is(":visible")) {
        resetIas(); // Resetta ias
        var pageElementsNotification = document.getElementsByClassName("myNotifications");
        var spinnerNotification = document.getElementsByClassName("spinnerNotification");
        if (pageElementsNotification.length > 0 && spinnerNotification.length > 0) {
            iasNotification = new InfiniteAjaxScroll('.containerMyNotifications', {
                item: '.myNotifications',
                next: '.pager__nextNotification',
                prev: '.pager__prevNotification',
                pagination: '.pagerNotification',
                spinner: {
                    // element to show as spinner
                    element: '.spinnerNotification',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 600,

                    // this function is called when the button has to be shown
                    show: function (element) {
                        element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function (element) {
                        element.style.opacity = '0'; // default behaviour
                    }
                }
            });

            iasNotification.on('page', (e) => {
                let contEv = $(".containerMyNotifications");
                if (contEv.is(":visible")) {
                    document.title = e.title;
                    let state = history.state;
                    history.replaceState(state, e.title, e.url);
                    bindPageLink(e.title, e.url);
                    bindPageFollow();
                    bindPageRemove();
                    bindPageRemoveNotification();
                }
            });

            // disable cache busting
            iasNotification.on('load', function (event) {
                event.nocache = true;
            });
        } else {
            bindPageFollow();
            bindPageRemove();
            bindPageRemoveNotification();
            bindPageSetPrimary();
            initializeTooltips();
        }
    }
}

function resetIas() {
    if (ias) {
        ias.off('page'); // Scollega l'evento 'page'
        ias.off('load'); // Scollega l'evento 'load'
        ias = null; // Resetta la variabile
    }
    if (iasFollow) {
        iasFollow.off('page'); // Scollega l'evento 'page'
        iasFollow.off('load'); // Scollega l'evento 'load'
        iasFollow = null; // Resetta la variabile
    }
    if (iasNotification) {
        iasNotification.off('page'); // Scollega l'evento 'page'
        iasNotification.off('load'); // Scollega l'evento 'load'
        iasNotification = null; // Resetta la variabile
    }
}
function bindTabChange() {
    $('#myTab button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
        var href = $(e.target).attr('data-bs-target');
        var tabId = href.substring(1);
        var pagesUri = new URI($('#accountPagesUri').attr('href'));
        if (tabId === 'mypages-tab-pane') {
            pagesUri.removeSearch("tab");
            pagesUri.addSearch("tab", "mypages");
            $('#mypages-tab-pane').load(pagesUri + ' #mypages-tab-pane > *', function () {
                loading = false;
                bindPageFollow();
                bindPageRemove();
                bindPageRemoveNotification();
                bindPageSetPrimary();
                bindIas();
                initializeTooltips();
            });
        } else if (tabId === 'followedpages-tab-pane') {
            pagesUri.removeSearch("tab");
            pagesUri.addSearch("tab", "follow");
            $('#followedpages-tab-pane').load(pagesUri + ' #followedpages-tab-pane > *', function () {
                loadingPagesFollow = false;
                bindPageFollow();
                bindPageRemove();
                bindPageRemoveNotification();
                bindPageSetPrimary();
                bindIas();
                initializeTooltips();
            });
        } else if (tabId === 'mynotifications-tab-pane') {
            pagesUri.removeSearch("tab");
            pagesUri.addSearch("tab", "mynotifications");
            $('#mynotifications-tab-pane').load(pagesUri + ' #mynotifications-tab-pane > *', function () {
                loadingPagesNotification = false;
                bindPageFollow();
                bindPageRemove();
                bindPageRemoveNotification();
                bindPageSetPrimary();
                bindIas();
                initializeTooltips();
            });
        }
    });
}