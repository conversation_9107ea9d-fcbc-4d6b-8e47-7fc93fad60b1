window.addEventListener("pageshow", function(evt){
    if(evt.persisted){
        if ($.cookie("agoraScrollPosition_home")) {
            var dataEventBack = $.cookie("agoraScrollPosition_home")
            const elementToScroll = document.querySelector("[data-wall-back=" + dataEventBack + "]");
            if (elementToScroll) {
                elementToScroll.scrollIntoView({block: 'center'});
            }
            $.removeCookie("agoraScrollPosition_home");
        }
    }
}, false);
if ('scrollRestoration' in history) {
  // Back off, browser, I got this...
  history.scrollRestoration = 'manual';
}
var loading = false;
var limit = parseInt($('#pagination').text(), 0);
var skip = 0;
var countCol = 0;


(function () {
    
    if ($.cookie("agoraScrollPosition_home")) {
        var dataProductBack = $.cookie("agoraScrollPosition_home")
        const elementToScroll = document.querySelector("[data-wall-back=" + dataProductBack + "]");
        if (elementToScroll) {
            setTimeout(function() {
                elementToScroll.scrollIntoView({block: 'center'});
                bindWallLink("agora", null);
            }, 1000);
        }
        $.removeCookie("agoraScrollPosition_home");
    }
    
    var wallEventElements = document.getElementsByClassName("wallEvent");
    var spinner = document.getElementsByClassName("spinner");
    if (wallEventElements.length > 0 && spinner.length > 0) {
        
        window.ias = new InfiniteAjaxScroll('.containerWallEvents', {
            item: '.wallEvent',
            next: '.pager__next',
            prev: '.pager__prev',
            pagination: '.pager',
            spinner: {
                // element to show as spinner
                element: '.spinner',

                // delay in milliseconds
                // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                // will be shown for the duration of the loading. If the loading takes less then this duration,
                // say 300ms, then the spinner is still shown for 600ms.
                delay: 600,

                // this function is called when the button has to be shown
                show: function(element) {
                  element.style.opacity = '1'; // default behaviour
                },

                // this function is called when the button has to be hidden
                hide: function(element) {
                  element.style.opacity = '0'; // default behaviour
                }
            }
        });


        ias.on('page', (e) => {
            document.title = e.title;
            let state = history.state;
            history.replaceState(state, e.title, e.url);
            bindWallLink(e.title, e.url);
            bindPageFollow();
            bindEventFollow();
            bindReportEvent();
        });

        // disable cache busting
        ias.on('load', function(event) {
          event.nocache = true;
        });
    } else {
        bindPageFollow();
        bindEventFollow();
        bindReportEvent();
    }

})();

function bindPageFollow() {
    $('.page-add-follow').off();
    $('.page-add-follow').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#pageFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var pageId = event.target.getAttribute('data-page-id');
        if (!pageId) {
            pageId = event.target.parentNode.getAttribute('data-page-id');
        }
        if (!pageId) {
            console.error('missing pageId');
            return false;
        }
        
        var value = event.target.getAttribute('data-value');
        if (!value) {
            value = event.target.parentNode.getAttribute('data-value');
        }
        if (!value) {
            console.error('missing value');
            return false;
        }
        
        var tinyReload = event.target.getAttribute('data-tiny');
        
        var idToReload;
        if (event.target.getAttribute('data-reload-id')) {
            idToReload = "#" + event.target.getAttribute('data-reload-id');
            
        } else {
            idToReload = "#" + $(event.target).parent().attr('data-reload-id');
        }
        
        if (user === 'unlogged' || user === 'notconfirmed') {
            var msgAlert = label('common.must.be.registered.page');
            
            if (user === 'notconfirmed') {
                msgAlert = 'Devi prima confermare il tuo account per poter seguire una pagina.';
                // warn
                Swal.fire({
                    position: 'top',
                    icon: 'error',
                    text: msgAlert,
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'btn btn-primary btn-lg',
                    },
                    confirmButtonText: label('common.continue'),
                    // todo
                    footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                });
            } else {
                // warn
                Swal.fire({
                    position: 'top',
                    icon: 'error',
                    text: msgAlert,
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'btn btn-primary btn-lg',
                    },
                    confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                    // todo
                    footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                });                
            }
            

            
            return false;
        }
        
        url.removeSearch("pageId");
        url.addSearch("pageId", pageId);

        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        var url = new URI();
                        $(idToReload).load(url + ' ' + idToReload + ' > *', function() {
                            bindEventFollow();
                            bindPageFollow();
                            if (tinyReload) {
                                e.tinySlider();
                            }
                            showToast(label('common.page.follow.added'), "success");
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        // warn
                        $.alert({
                            theme: 'supervan',
                            escapeKey: true,
                            animation: 'top',
                            closeAnimation: 'bottom',
                            backgroundDismiss: true,
                            title: 'Oh oh! :(',
                            content: label('common.add.failed')
                        });
                    }
        });                    

        return false;
    });
}

function bindEventFollow() {
    $('.event-add-follow').off();
    $('.event-add-follow').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#eventFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var eventId = event.target.getAttribute('data-event-id');
        if (!eventId) {
            eventId = event.target.parentNode.getAttribute('data-event-id');
        }
        if (!eventId) {
            console.error('missing eventId');
            return false;
        }
        
        var value = event.target.getAttribute('data-value');
        if (!value) {
            value = event.target.parentNode.getAttribute('data-value');
        }
        if (!value) {
            console.error('missing value');
            return false;
        }
        
        var idToReload;
        if (event.target.getAttribute('data-reload-id')) {
            idToReload = "#" + event.target.getAttribute('data-reload-id');
        } else {
            idToReload = "#" + $(event.target).parent().attr('data-reload-id');
        }
        
        if (user === 'unlogged' || user === 'notconfirmed') {
            var msgAlert = label('common.must.registered.event');
            
            if (user === 'notconfirmed') {
                msgAlert = 'Devi prima confermare il tuo account per poter seguire un evento.';
                // warn
                Swal.fire({
                    position: 'top',
                    icon: 'error',
                    title: 'Oh oh! :(',
                    text: msgAlert,
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'btn btn-primary btn-lg',
                    },
                    confirmButtonText: label('common.continue'),
                    // todo
                    footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                });
            } else  {
                // warn
                Swal.fire({
                    position: 'top',
                    icon: 'error',
                    title: 'Oh oh! :(',
                    text: msgAlert,
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'btn btn-primary btn-lg',
                    },
                    confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                    // todo
                    footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                });
                
            }
            
            return false;
        }
        
        url.removeSearch("eventId");
        url.addSearch("eventId", eventId);

        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    // If follow was added and there are pages to show
                    if (response.action === 'added' && response.showPagesModal && response.pages && response.pages.length > 0) {
                        // Show success message first
                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: label('event.follow.success'),
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function(){
                            // Then show the pages modal
                            showFollowPagesModal(response.pages);
                        });
                    }

                    // Standard flow - reload the section
                    var homeUri = new URI($('#homeUri').attr('href'));
                    homeUri.removeSearch("eventId");
                    homeUri.addSearch("eventId", eventId);
                    $(idToReload).load(homeUri + ' ' + idToReload + ' > *', function() {
                        bindEventFollow();
                    });
                } else {
                    // Handle error response
                    $.alert({
                        theme: 'supervan',
                        escapeKey: true,
                        animation: 'top',
                        closeAnimation: 'bottom',
                        backgroundDismiss: true,
                        title: 'Oh oh! :(',
                        content: response.error || label('common.add.failed')
                    });
                }
            },
            error: function (response, status, errorThrown) {
                // warn
                $.alert({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: 'Oh oh! :(',
                    content: label('common.add.failed')
                });
            }
        });

        return false;
    });
}

function bindWallLink(title, url) {
    $('.walllink').off();
    $('.walllink').click(function(event) {
        let state = history.state;
        if (url) {
            history.replaceState(state, title, url);
        } else {
            history.replaceState(state, title, window.location.href);
        }
        $.cookie("agoraScrollPosition_home" , $(this).attr("data-wall-back"), { expires: 1 });
    });
}

function bindReportEvent() {
    
    $('#modalReportEvent').on('show.bs.modal', function (event) {
      var button = $(event.relatedTarget); // Il pulsante che ha attivato il modale
      var eventId = button.data('eventid'); // Estrai l'informazione da data-eventId
      var modal = $(this);
      modal.find('#eventId').val(eventId); // Imposta il valore del campo nascosto
    });
    
    $('.report-event-btn').off();
    $('.report-event-btn').click(function (event) {
        event.preventDefault();
        
        if ($("#report-event").valid()) {
            
            // prepare call
            var actionURL = $('#report-event').attr('action');
            var url = new URI(actionURL);
            if (!url) {
                console.error('missing url');
                return false;
            }

            var user = event.target.getAttribute('data-user');
            if (!user) {
                user = event.target.parentNode.getAttribute('data-user');
            }
            if (!user) {
                console.error('missing user');
                return false;
            }

            if (user === 'unlogged') {

                // warn
                Swal.fire({
                    position: 'top',
                    icon: 'error',
                    title: 'Oh oh! :(',
                    text: label('common.must.be.registered.report.event'),
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'btn btn-primary btn-lg',
                    },
                    confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                    // todo
                    footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                });


                return false;
            }

            var formData = new FormData($('#report-event')[0]);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();

                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: 'Evento segnalato',
                                showConfirmButton: false,
                                timer: 1500
                            }).then(function(){
                                window.location.reload();
                            });

                        },
                error:
                        function (response, status, errorThrown) {
                            $.unblockUI();

                         // warn
                            Swal.fire({
                                position: 'top',
                                icon: 'error',
                                title: 'Oh oh! :(',
                                text: label('common.operation.error'),
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: label('common.continue'),
                                // todo
                                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                            });
                        }
            });                    

            return false;
        }
    });
}

function showToast(message, type = 'info') {
    const toastId = 'toast-' + Date.now();

    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0 mb-2" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>`;

    // Se il contenitore non esiste, lo crea
    if (!document.getElementById("toast-container")) {
        $('body').append(`<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;"></div>`);
    }

    $('#toast-container').append(toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 3000 }); // 3 secondi
    toast.show();

    // Rimozione dal DOM dopo chiusura
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

function showFollowPagesModal(pages) {
    // Clear previous content
    $('#followPagesContainer').empty();

    if (!pages || pages.length === 0) {
        return;
    }

    // Build pages list
    var pagesHtml = '';
    pages.forEach(function(page) {
        var isFollowing = page.isFollowing || false;
        var buttonClass = isFollowing ? 'btn-outline-danger' : 'btn-outline-primary';
        var buttonIcon = isFollowing ? 'bi-dash-circle-fill' : 'bi-plus-circle-fill';
        var buttonText = isFollowing ? label('common.unfollow') : label('common.follow');
        var buttonAction = isFollowing ? 'unfollow' : 'follow';

        // Build image URL if available
        var imageHtml = '';
        if (page.profileImageId) {
            var imageUrl = $('#imageUri').attr('href') + '?oid=' + page.profileImageId;
            imageHtml = '<img src="' + imageUrl + '" alt="' + page.name + '" class="rounded-circle me-3" width="50" height="50">';
        } else {
            imageHtml = '<div class="bg-light rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;"><i class="bi bi-person-fill text-muted"></i></div>';
        }

        var url = $("#pageDetailUri").attr("href").replace(":identifier", page.identifier);
        pagesHtml += '<div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3" data-page-id="' + page.id + '">' +
            '<div class="d-flex align-items-center">' +
                imageHtml +
                '<div>' +
                    '<h6 class="mb-0"><a href="' + url + '" target="_blank">' + page.name + '</a></h6>' +
                    (page.shortDescription ? '<p class="mb-0 small">' + page.shortDescription + '</p>' : '') +
                '</div>' +
            '</div>' +
            '<button class="btn ' + buttonClass + ' btn-sm page-follow-toggle" ' +
                'data-page-id="' + page.id + '" ' +
                'data-action="' + buttonAction + '" ' +
                'data-following="' + isFollowing + '">' +
                '<i class="' + buttonIcon + ' me-1"></i>' +
                buttonText +
            '</button>' +
        '</div>';
    });

    $('#followPagesContainer').html(pagesHtml);

    // Bind click events for individual page follow buttons
    bindPageFollowToggle();

    // Show the modal
    var modal = new bootstrap.Modal(document.getElementById('followPagesModal'));
    modal.show();
}

function bindPageFollowToggle() {
    $('.page-follow-toggle').off('click').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var pageId = button.data('page-id');
        var action = button.data('action');
        var isCurrentlyFollowing = button.data('following');

        // Toggle the button state immediately for better UX
        togglePageFollowButton(button, !isCurrentlyFollowing);

        // Make AJAX call to toggle follow status
        var url = new URI($('#pageFollowToggleUri').attr('href'));
        url.addSearch('pageId', pageId);

        $.ajax({
            url: url.toString(),
            type: 'POST',
            // dataType: 'json',
            success: function(response) {
                // Button state already updated, just update data attribute
                button.data('following', !isCurrentlyFollowing);
                button.data('action', !isCurrentlyFollowing ? 'unfollow' : 'follow');
            },
            error: function(xhr, status, error) {
                // Revert button state on error
                togglePageFollowButton(button, isCurrentlyFollowing);
                console.error('Error toggling page follow:', error);
            }
        });
    });
}

function togglePageFollowButton(button, isFollowing) {
    if (isFollowing) {
        button.removeClass('btn-outline-primary').addClass('btn-outline-danger');
        button.find('i').removeClass('bi-plus-circle-fill').addClass('bi-dash-circle-fill');
        button.contents().filter(function() { return this.nodeType === 3; }).last().replaceWith(label('common.unfollow'));
        button.data('action', 'unfollow');
    } else {
        button.removeClass('btn-outline-danger').addClass('btn-outline-primary');
        button.find('i').removeClass('bi-dash-circle-fill').addClass('bi-plus-circle-fill');
        button.contents().filter(function() { return this.nodeType === 3; }).last().replaceWith(label('common.follow'));
        button.data('action', 'follow');
    }
    button.data('following', isFollowing);
}
