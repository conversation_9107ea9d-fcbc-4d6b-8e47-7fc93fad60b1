window.addEventListener("pageshow", function (evt) {
    if (evt.persisted) {
        if ($.cookie("agoraScrollPosition_events")) {
            var dataEventBack = $.cookie("agoraScrollPosition_events")
            const elementToScroll = document.querySelector("[data-events-back=" + dataEventBack + "]");
            if (elementToScroll) {
                elementToScroll.scrollIntoView({block: 'center'});
            }
            $.removeCookie("agoraScrollPosition_events");
        }
    }
}, false);
if ('scrollRestoration' in history) {
    // Back off, browser, I got this...
    history.scrollRestoration = 'manual';
}
var loading = false;
var limit = parseInt($('#pagination').text(), 0);
var skip = 0;

var loadingFollow = false;
var limitFollow = parseInt($('#paginationFollow').text(), 0);
var skipFollow = 0;
var citiesAcUri = $('#dataCitiesUri').attr('href');

(function () {

    if ($.cookie("agoraScrollPosition_events")) {
        var dataProductBack = $.cookie("agoraScrollPosition_events")
        const elementToScroll = document.querySelector("[data-events-back=" + dataProductBack + "]");
        if (elementToScroll) {
            setTimeout(function () {
                elementToScroll.scrollIntoView({block: 'center'});
                bindWallLink("agora", null);
            }, 1000);
        }
        $.removeCookie("agoraScrollPosition_events");
    }

    bindIas();

    bindChangeTag();
    bindChangeCategory();
    bindChangeDate();
    bindChangeCity();

    $('#event-tabs a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
        var href = $(e.target).attr('href');
        var tabId = href.substring(1);
        var eventsUri = new URI($('#eventsUri').attr('href'));
        if (tabId === 'all-events') {
            eventsUri.removeSearch("tab");
            eventsUri.addSearch("tab", "all");
            $('#all-events').load(eventsUri + ' #all-events > *', function () {
                bindEventFollow();
                bindIas();
            });
        } else if (tabId === 'follow-events') {
            eventsUri.removeSearch("tab");
            eventsUri.addSearch("tab", "follow");
            $('#follow-events').load(eventsUri + ' #follow-events > *', function () {
                bindEventFollow();
                bindIas();
            });
        }
    });

    $('#city').select2({
        minimumInputLength: 3,
        ajax: {
            url: $('#dataCitiesUri').attr('href'),
            dataType: 'json',
            delay: 250,
            quietMillis: 250,
            data: function (term) {
                return {name: term};
            },
            processResults: function (data) {
                var results;
                results = [];
                $.each(data, function (idx, item) {
                    results.push({
                        'id': item[0],
                        'text': item[0]
                    });
                });
                return {results: results};
            }
        },
        createTag: function (params) {
            // Non permette la creazione di nuovi tag basati sul testo digitato
            return null;
        }
    });
    
    $('#tag').select2({})
})();

function bindEventFollow() {
    $('.event-add-follow').off();
    $('.event-add-follow').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#eventFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }

        var eventId = event.target.getAttribute('data-event-id');
        if (!eventId) {
            eventId = event.target.parentNode.getAttribute('data-event-id');
        }
        if (!eventId) {
            console.error('missing eventId');
            return false;
        }

        var value = event.target.getAttribute('data-value');
        if (!value) {
            value = event.target.parentNode.getAttribute('data-value');
        }
        if (!value) {
            console.error('missing value');
            return false;
        }

        var idToReload;
        if (event.target.getAttribute('data-reload-id')) {
            idToReload = "#" + event.target.getAttribute('data-reload-id');
        } else {
            idToReload = "#" + $(event.target).parent().attr('data-reload-id');
        }
        
        if (user === 'unlogged' || user === 'notconfirmed') {
            var msgAlert = label('common.must.registered.event');

            if (user === 'notconfirmed') {
                msgAlert = 'Devi prima confermare il tuo account per poter seguire un evento.';
                // warn
                Swal.fire({
                    position: 'top',
                    icon: 'error',
                    title: 'Oh oh! :(',
                    text: msgAlert,
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'btn btn-primary btn-lg',
                    },
                    confirmButtonText: label('common.continue'),
                    // todo
                    footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                });
            } else {
                // warn
                Swal.fire({
                    position: 'top',
                    icon: 'error',
                    title: 'Oh oh! :(',
                    text: msgAlert,
                    buttonsStyling: false,
                    customClass: {
                        confirmButton: 'btn btn-primary btn-lg',
                    },
                    confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                    // todo
                    footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                });
            }


            return false;
        }

        url.removeSearch("eventId");
        url.addSearch("eventId", eventId);

        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function (response) {
                if (response.success) {
                    // If follow was added and there are pages to show
                    if (response.action === 'added' && response.showPagesModal && response.pages && response.pages.length > 0) {
                        // Show success message first
                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: label('event.follow.success'),
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function(){
                            // Then show the pages modal
                            showFollowPagesModal(response.pages);
                        });
                    } else {
                        // Standard flow - reload the section
                        var eventsUri = new URI($('#eventsUri').attr('href'));
                        eventsUri.removeSearch("eventId");
                        eventsUri.addSearch("eventId", eventId);
                        $(idToReload).load(eventsUri + ' ' + idToReload + ' > *', function () {
                            bindEventFollow();
                        });
                    }
                } else {
                    // Handle error response
                    $.alert({
                        theme: 'supervan',
                        escapeKey: true,
                        animation: 'top',
                        closeAnimation: 'bottom',
                        backgroundDismiss: true,
                        title: 'Oh oh! :(',
                        content: response.error || label('common.add.failed')
                    });
                }
            },
            error: function (response, status, errorThrown) {
                // warn
                $.alert({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: 'Oh oh! :(',
                    content: label('common.add.failed')
                });
            }
        });

        return false;
    });
}

function bindWallLink(title, url) {
    $('.walllink').off();
    $('.walllink').click(function (event) {
        let state = history.state;
        if (url) {
            history.replaceState(state, title, url);
        } else {
            history.replaceState(state, title, window.location.href);
        }
        $.cookie("agoraScrollPosition_events", $(this).attr("data-events-back"), {expires: 1});
    });
}

function bindIas() {

    var eventElements = document.getElementsByClassName("wallEvent");
    var spinner = document.getElementsByClassName("spinner");
    var containerWallEvents = $(".containerWallEvents");
    var containerWallEventsFollow = $(".containerWallEventsFollow");
    if (containerWallEvents.is(":visible")) {
        if (eventElements.length > 0) {

            window.ias = new InfiniteAjaxScroll('.containerWallEvents', {
                item: '.wallEvent',
                next: '.pager__next',
                prev: '.pager__prev',
                pagination: '.pager',
                spinner: {
                    // element to show as spinner
                    element: '.spinner',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 300,

                    // this function is called when the button has to be shown
                    show: function (element) {
                        element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function (element) {
                        element.style.opacity = '0'; // default behaviour
                    }
                }
            });


            ias.on('page', (e) => {
                document.title = e.title;
                let state = history.state;
                history.replaceState(state, e.title, e.url);
                bindWallLink(e.title, e.url);
                bindEventFollow();
            });

            // disable cache busting
            ias.on('load', function (event) {
                event.nocache = true;
            });
        } else {
            bindEventFollow();
        }
    } else if (containerWallEventsFollow.is(":visible")) {
        var eventElementsFollow = document.getElementsByClassName("wallEventFollow");
        var spinnerFollow = document.getElementsByClassName("spinnerFollow");
        if (eventElementsFollow.length > 0 && spinnerFollow.length > 0) {

            window.ias = new InfiniteAjaxScroll('.containerWallEventsFollow', {
                item: '.wallEventFollow',
                next: '.pager__nextFollow',
                prev: '.pager__prevFollow',
                pagination: '.pagerFollow',
                spinner: {
                    // element to show as spinner
                    element: '.spinnerFollow',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 600,

                    // this function is called when the button has to be shown
                    show: function (element) {
                        element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function (element) {
                        element.style.opacity = '0'; // default behaviour
                    }
                }
            });


            ias.on('page', (e) => {
                document.title = e.title;
                let state = history.state;
                history.replaceState(state, e.title, e.url);
                bindWallLink(e.title, e.url);
                bindEventFollow();
            });

            // disable cache busting
            ias.on('load', function (event) {
                event.nocache = true;
            });
        } else {
            bindEventFollow();
        }
    }
}

function bindChangeCategory() {
    $('#category').change(function (event) {

        var url = new URI($('#eventsUri').attr('href'));

        var category = $("#category").val();
        if (category) {
            url.segment(category);
        } else {
            url.removeSearch("category");
        }

        var tag = $("#tag").val();
        if (tag) {
            url.addSearch("tag", tag);
        } else {
            url.removeSearch("tag");
        }
        
        var startDate = $("#daterange-predefined").data('daterangepicker').startDate.format('DD/MM/YYYY');
        url.addSearch("startDate", startDate);
        var endDate = $("#daterange-predefined").data('daterangepicker').endDate.format('DD/MM/YYYY');
        url.addSearch("endDate", endDate);

        var city = $("#city").val();
        if (city) {
            url.addSearch("city", city);
        }

        $('#all-events').load(url + ' #all-events > *', function () {
            window.history.replaceState(null, null, url.toString());
            bindEventFollow();
            bindIas();
        });

    });
}

function bindChangeTag() {
    $('#tag').change(function (event) {

        var url = new URI($('#eventsUri').attr('href'));


        var category = $("#category").val();
        if (category) {
            url.segment(category);
        } else {
            url.removeSearch("category");
        }
        
        var tag = $("#tag").val();
        if (tag) {
            url.addSearch("tag", tag);
        } else {
            url.removeSearch("tag");
        }
        
        var startDate = $("#daterange-predefined").data('daterangepicker').startDate.format('DD/MM/YYYY');
        url.addSearch("startDate", startDate);
        var endDate = $("#daterange-predefined").data('daterangepicker').endDate.format('DD/MM/YYYY');
        url.addSearch("endDate", endDate);

        $('#all-events').load(url + ' #all-events > *', function () {
            window.history.replaceState(null, null, url.toString());
            bindEventFollow();
            bindIas();
        });

    });
}

function bindChangeDate() {
    $('#daterange-predefined').on('apply.daterangepicker', function (ev, picker) {
        event.preventDefault();

        var url = new URI($('#eventsUri').attr('href'));

        var category = $("#category").val();
        if (category) {
            url.segment(category);
        } else {
            url.removeSearch("category");
        }
        
        var tag = $("#tag").val();
        if (tag) {
            url.addSearch("tag", tag);
        } else {
            url.removeSearch("tag");
        }

        var startDate = picker.startDate.format('DD/MM/YYYY');
        url.addSearch("startDate", startDate);
        var endDate = picker.endDate.format('DD/MM/YYYY');
        url.addSearch("endDate", endDate);

        var city = $("#city").val();
        if (city) {
            url.addSearch("city", city);
        }

        $('#all-events').load(url + ' #all-events > *', function () {
            window.history.replaceState(null, null, url.toString());
            bindEventFollow();
            bindIas();
        });

    });
}

function bindChangeCity() {
    $('#city').change(function (event) {

        var url = new URI($('#eventsUri').attr('href'));

        var category = $("#category").val();
        if (category) {
            url.segment(category);
        } else {
            url.removeSearch("category");
        }

        var tag = $("#tag").val();
        if (tag) {
            url.segment(tag);
        } else {
            url.removeSearch("tag");
        }

        var startDate = $("#daterange-predefined").data('daterangepicker').startDate.format('DD/MM/YYYY');
        url.addSearch("startDate", startDate);
        var endDate = $("#daterange-predefined").data('daterangepicker').endDate.format('DD/MM/YYYY');
        url.addSearch("endDate", endDate);

        var city = $("#city").val();
        if (city) {
            url.addSearch("city", city);
        }

        $('#all-events').load(url + ' #all-events > *', function () {
            window.history.replaceState(null, null, url.toString());
            bindEventFollow();
            bindIas();
        });

    });
}

function showFollowPagesModal(pages) {
    // Clear previous content
    $('#followPagesContainer').empty();

    if (!pages || pages.length === 0) {
        return;
    }

    // Build pages list
    var pagesHtml = '';
    pages.forEach(function(page) {
        var isFollowing = page.isFollowing || false;
        var buttonClass = isFollowing ? 'btn-outline-danger' : 'btn-outline-primary';
        var buttonIcon = isFollowing ? 'bi-dash-circle-fill' : 'bi-plus-circle-fill';
        var buttonText = isFollowing ? label('common.unfollow') : label('common.follow');
        var buttonAction = isFollowing ? 'unfollow' : 'follow';

        // Build image URL if available
        var imageHtml = '';
        if (page.profileImageId) {
            var imageUrl = $('#imageUri').attr('href') + '?oid=' + page.profileImageId;
            imageHtml = '<img src="' + imageUrl + '" alt="' + page.name + '" class="rounded-circle me-3" width="50" height="50">';
        } else {
            imageHtml = '<div class="bg-light rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;"><i class="bi bi-person-fill text-muted"></i></div>';
        }

        var url = $("#pageDetailUri").attr("href").replace(":identifier", page.identifier);
        pagesHtml += '<div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3" data-page-id="' + page.id + '">' +
            '<div class="d-flex align-items-center">' +
                imageHtml +
                '<div>' +
                    '<h6 class="mb-0"><a href="' + url + '" target="_blank">' + page.name + '</a></h6>' +
                    (page.shortDescription ? '<p class="mb-0 small">' + page.shortDescription + '</p>' : '') +
                '</div>' +
            '</div>' +
            '<button class="btn ' + buttonClass + ' btn-sm page-follow-toggle" ' +
                'data-page-id="' + page.id + '" ' +
                'data-action="' + buttonAction + '" ' +
                'data-following="' + isFollowing + '">' +
                '<i class="' + buttonIcon + ' me-1"></i>' +
                buttonText +
            '</button>' +
        '</div>';
    });

    $('#followPagesContainer').html(pagesHtml);

    // Bind click events for individual page follow buttons
    bindPageFollowToggle();

    // Show the modal
    var modal = new bootstrap.Modal(document.getElementById('followPagesModal'));
    modal.show();
}

function bindPageFollowToggle() {
    $('.page-follow-toggle').off('click').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var pageId = button.data('page-id');
        var action = button.data('action');
        var isCurrentlyFollowing = button.data('following');

        // Toggle the button state immediately for better UX
        togglePageFollowButton(button, !isCurrentlyFollowing);

        // Make AJAX call to toggle follow status
        var url = new URI($('#pageFollowToggleUri').attr('href'));
        url.addSearch('pageId', pageId);

        $.ajax({
            url: url.toString(),
            type: 'POST',
            dataType: 'json',
            success: function(response) {
                // Button state already updated, just update data attribute
                button.data('following', !isCurrentlyFollowing);
                button.data('action', !isCurrentlyFollowing ? 'unfollow' : 'follow');
            },
            error: function(xhr, status, error) {
                // Revert button state on error
                togglePageFollowButton(button, isCurrentlyFollowing);
                console.error('Error toggling page follow:', error);
            }
        });
    });
}

function togglePageFollowButton(button, isFollowing) {
    if (isFollowing) {
        button.removeClass('btn-outline-primary').addClass('btn-outline-danger');
        button.find('i').removeClass('bi-plus-circle-fill').addClass('bi-dash-circle-fill');
        button.contents().filter(function() { return this.nodeType === 3; }).last().replaceWith(label('common.unfollow'));
        button.data('action', 'unfollow');
    } else {
        button.removeClass('btn-outline-danger').addClass('btn-outline-primary');
        button.find('i').removeClass('bi-dash-circle-fill').addClass('bi-plus-circle-fill');
        button.contents().filter(function() { return this.nodeType === 3; }).last().replaceWith(label('common.follow'));
        button.data('action', 'follow');
    }
    button.data('following', isFollowing);
}