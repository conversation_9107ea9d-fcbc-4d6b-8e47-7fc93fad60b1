(function () {
    bindEventFollow();
    bindDisconnectChildren();
    setTimeout(function () {
        initEventFiltering();
        initEventNavigation();
    }, 250);
})();

function bindEventFollow() {
    $('.event-add-follow').off();
    $('.event-add-follow').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#eventFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var eventId = event.target.getAttribute('data-event-id');
        if (!eventId) {
            eventId = event.target.parentNode.getAttribute('data-event-id');
        }
        if (!eventId) {
            console.error('missing eventId');
            return false;
        }
        
        var value = event.target.getAttribute('data-value');
        if (!value) {
            value = event.target.parentNode.getAttribute('data-value');
        }
        if (!value) {
            console.error('missing value');
            return false;
        }
        
        if (user === 'unlogged') {
            
            // warn
            Swal.fire({
                position: 'top',
                icon: 'error',
                title: 'Oh oh! :(',
                text: label('common.must.registered.event'),
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary btn-lg',
                },
                confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                // todo
                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
            });

            
            return false;
        }
        
        url.removeSearch("eventId");
        url.addSearch("eventId", eventId);

        var idToReload;
        if (event.target.getAttribute('data-reload-id')) {
            idToReload = "#" + event.target.getAttribute('data-reload-id');
        } else {
            idToReload = "#" + $(event.target).parent().attr('data-reload-id');
        }
        
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function (response) {
                $.unblockUI();

                if (response.success) {
                    var msg = label('common.event.added');
                    if (response.action === 'removed') {
                        msg = label('common.event.removed');
                    }

                    // If follow was added and there are pages to show
                    if (response.action === 'added' && response.showPagesModal && response.pages && response.pages.length > 0) {
                        // Show success message first
                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function(){
                            // Then show the pages modal
                            showFollowPagesModal(response.pages);
                        });
                    } else {
                        // Standard flow - show success and reload
                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function(){
                            window.location.reload();
                        });
                    }
                    
                    // Standard flow - reload the section
                    var eventUri = new URI($('#eventUri').attr('href'));
                    $(idToReload).load(eventUri + ' ' + idToReload + ' > *', function() {
                        bindEventFollow();
                    });
                } else {
                    // Handle error response
                    $.alert({
                        theme: 'supervan',
                        escapeKey: true,
                        animation: 'top',
                        closeAnimation: 'bottom',
                        backgroundDismiss: true,
                        title: 'Oh oh! :(',
                        content: response.error || label('common.add.failed')
                    });
                }
            },
            error: function (response, status, errorThrown) {
                $.unblockUI();

                // warn
                $.alert({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: 'Oh oh! :(',
                    content: label('common.add.failed')
                });
            }
        });

        return false;
    });
}

// Event filtering functionality
function initEventFiltering() {
    // Check if there are container events to filter
    var containerEvents = $('.containerEvents');
    if (containerEvents.length === 0) {
        console.log('No container events found, filtering not needed');
        return;
    }

    // Get current event ID from the page
    var eventId = getEventIdFromPage();
    if (!eventId) {
        console.warn('Event ID not found, filtering disabled');
        // Hide filter controls if no event ID
        $('.card-header').has('#pageFilter, #pageFilterHorizontal').hide();
        return;
    }

    // Load pages for dropdown
    loadPagesForEvent(eventId);

    // Check for URL parameters and auto-select
    // checkUrlParameters();

    // Bind filter change events
    bindFilterEvents();

    // Set up browser navigation handling
    setupBrowserNavigation();
}

function getEventIdFromPage() {
    /*// Try to get event ID from various possible sources
    var eventId = null;

    // Try from meta tag
    var metaTag = $('meta[name="event-id"]');
    if (metaTag.length > 0) {
        eventId = metaTag.attr('content');
    }

    // Try from data attribute on body or main container
    if (!eventId) {
        eventId = $('body').data('event-id') || $('.main-content').data('event-id');
    }

    // Try from event follow button data attribute
    if (!eventId) {
        var followButton = $('.event-add-follow');
        if (followButton.length > 0) {
            eventId = followButton.attr('data-event-id');
        }
    }

    // Try from hidden input or data attribute
    if (!eventId) {
        var hiddenInput = $('input[name="eventId"]');
        if (hiddenInput.length > 0) {
            eventId = hiddenInput.val();
        }
    }

    // Try to extract from URL path (last resort)
    if (!eventId) {
        var pathParts = window.location.pathname.split('/');
        var eventIndex = -1;
        for (var i = 0; i < pathParts.length; i++) {
            if (pathParts[i] === 'evento' || pathParts[i] === 'event') {
                eventIndex = i;
                break;
            }
        }
        if (eventIndex >= 0 && pathParts.length > eventIndex + 1) {
            // This is the event identifier, we'll need to make an API call to get the ID
            // For now, we'll use the identifier as a fallback
            var eventIdentifier = pathParts[eventIndex + 1];
            if (eventIdentifier && eventIdentifier.length > 0) {
                // We could make an API call here to convert identifier to ID
                // For now, we'll return the identifier and handle it in the API calls
                eventId = eventIdentifier;
            }
        }
    }*/

    var eventId = $('#eventId').text();
    return eventId;
}

function loadPagesForEvent(eventId) {
    var url = new URI($('#eventPagesUri').attr('href'));
    url.addSearch('eventId', eventId);

    $.ajax({
        url: url.toString(),
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            var parsedResponse = JSON.parse(response);
            if (parsedResponse.success && parsedResponse.pages) {
                populatePageDropdowns(parsedResponse.pages);

                checkUrlParameters();
            } else {
                console.warn('Failed to load pages:', parsedResponse.error || 'Unknown error');
                // Hide filter controls if pages can't be loaded
                $('#pageFilter, #pageFilterHorizontal').closest('.col-md-6').hide();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading pages:', error);
            // Hide filter controls if there's an error
            $('#pageFilter, #pageFilterHorizontal').closest('.col-md-6').hide();
        }
    });
}

function populatePageDropdowns(pages) {
    var pageOptions = '<option value="">' + (label('common.all.pages') || 'All Pages') + '</option>';

    pages.forEach(function(page) {
        /*var imageHtml = '';
        if (page.profileImageId) {
            imageHtml = '<img src="' + getImageUrl(page.profileImageId) + '" alt="' + page.name + '" style="width: 20px; height: 20px; border-radius: 50%; margin-right: 8px;">';
        }*/

        pageOptions += '<option value="' + page.id + '" data-image="' + (page.profileImageId || '') + '">' + /*imageHtml + */page.name + '</option>';
    });

    // Populate both dropdowns
    $('#pageFilter, #pageFilterHorizontal').html(pageOptions);

    // Initialize Select2 for multi-select functionality
    $('#pageFilter, #pageFilterHorizontal').select2({
        placeholder: label('common.all.pages') || 'All Pages',
        allowClear: true,
        multiple: true,
        closeOnSelect: false,
        width: '100%'
    });
}

function getImageUrl(imageId) {
    // Build image URL based on the application's image system
    var basePath = $("#imageUri").attr("href");
    return basePath + '?oid=' + imageId;
}

function bindFilterEvents() {
    // Bind change events to both filter sets
    $('#pageFilter, #pageFilterHorizontal').on('change', function() {
        var filterId = $(this).attr('id');
        var otherFilterId = filterId === 'pageFilter' ? 'pageFilterHorizontal' : 'pageFilter';

        // Get selected values (array for multi-select)
        var selectedValues = $(this).val();

        // Sync the other dropdown with Select2
        $('#' + otherFilterId).val(selectedValues).trigger('change.select2');

        // Apply filters
        applyFilters();
    });

    $('#sortOrder, #sortOrderHorizontal').on('change', function() {
        var filterId = $(this).attr('id');
        var otherFilterId = filterId === 'sortOrder' ? 'sortOrderHorizontal' : 'sortOrder';

        // Sync the other dropdown
        $('#' + otherFilterId).val($(this).val());

        // Apply filters
        applyFilters();
    });
}

function applyFilters() {
    var pageIds = $('#pageFilter').val() || $('#pageFilterHorizontal').val();
    var sortOrder = $('#sortOrder').val() || $('#sortOrderHorizontal').val();

    // Show loading state
    showLoadingState();

    var url = new URI($('#eventListUri').attr('href'));
    var eventId = getEventIdFromPage();
    if (eventId) {
        url.addSearch('eventId', eventId);
    }

    // Handle pageIds - can be array (multi-select) or single value
    if (pageIds && pageIds.length > 0) {
        // Filter out empty values
        var validPageIds = Array.isArray(pageIds) ?
            pageIds.filter(function(id) { return id && id !== ''; }) :
            (pageIds !== '' ? [pageIds] : []);

        if (validPageIds.length > 0) {
            // Join multiple pageIds with comma for backend processing
            url.addSearch('pageId', validPageIds.join(','));
        }
    }

    if (sortOrder) {
        url.addSearch('sortOrder', sortOrder);
    }

    $.ajax({
        url: url.toString(),
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            hideLoadingState();

            var parsedResponse = JSON.parse(response);
            if (parsedResponse.success && parsedResponse.events) {
                updateEventsList(parsedResponse.events);

                // Convert pageIds array back to string for URL state
                var pageIdParam = pageIds && pageIds.length > 0 ?
                    (Array.isArray(pageIds) ? pageIds.filter(function(id) { return id && id !== ''; }).join(',') : pageIds) :
                    null;

                updateUrlState(pageIdParam, sortOrder);

                // Show success feedback for filtered results
                if (pageIdParam || (sortOrder && sortOrder !== 'desc')) {
                    showFilterFeedback(parsedResponse.events.length, pageIdParam, sortOrder);
                }
            } else {
                console.warn('Failed to load events:', parsedResponse.error || 'Unknown error');
                showErrorState();
            }
        },
        error: function(xhr, status, error) {
            hideLoadingState();
            console.error('Error loading events:', error);
            showErrorState();
        }
    });
}

function updateEventsList(events) {
    // Update both vertical and horizontal layouts
    updateEventsContainer('.containerEvents .oneEvent', events);
}

function updateEventsContainer(containerSelector, events) {
    var container = $(containerSelector);
    if (container.length === 0) {
        return;
    }

    // Clear existing events
    container.empty();

    if (events.length === 0) {
        container.html('<div class="text-center py-4"><p class="text-muted">' +
                      (label('common.no.events.found') || 'No events found') + '</p></div>');
        return;
    }

    // Build events HTML
    var eventsHtml = '';
    events.forEach(function(event, index) {
        var isPastEvent = event.endDate ? (new Date(event.endDate) < new Date()) :
                         (event.startDate ? (new Date(event.startDate) < new Date()) : false);

        var eventPathBase = $('#eventPathBaseUri').attr('href');

        var eventHtml = '<div class="row">' +
            '<div class="d-sm-flex align-items-center ' + (isPastEvent ? 'opacity-50' : '') + '">' +
                '<div class="avatar avatar-xl">' +
                    '<a href="' + eventPathBase + '/' + event.identifier + '" class="eventlink">';

        // Event image
        if (event.coverImageId) {
            eventHtml += '<img class="avatar-img border border-white border-3" src="' + getImageUrl(event.coverImageId) + '" alt="' + event.name + '">';
        } else {
            eventHtml += '<img class="avatar-img border border-white border-3" src="/fe/images/bg/placeholder-event.jpg" alt="' + event.name + '">';
        }

        eventHtml += '</a></div>' +
            '<div class="ms-sm-4 mt-2 mt-sm-0">' +
                '<h5 class="mb-1"><a href="' + eventPathBase + '/' + event.identifier + '">' + event.name + '</a></h5>' +
                '<ul class="nav nav-stack small">';

        // Event date and time
        if (event.startDate) {
            var startDate = new Date(event.startDate);
            eventHtml += '<li class="nav-item">' +
                '<i class="bi bi-calendar-check pe-1"></i>' + formatEventDate(startDate) +
                (event.startHour ? ' ' + event.startHour : '') + '</li>';
        }

        // Event location
        if (event.city) {
            eventHtml += '<li class="nav-item"><i class="bi bi-geo-alt pe-1"></i>' + event.city + '</li>';
        }

        // Follower count
        if (event.followerCount > 0) {
            eventHtml += '<li class="nav-item"><i class="bi bi-people pe-1"></i>' + event.followerCount + ' ' +
                        (label('common.participants') || 'participants') + '</li>';
        }

        eventHtml += '</ul></div></div></div>';

        // Add separator except for last item
        if (index < events.length - 1) {
            eventHtml += '<hr>';
        }

        eventsHtml += eventHtml;
    });

    container.html(eventsHtml);
}

function formatEventDate(date) {
    // Simple date formatting - can be enhanced based on locale
    var options = { weekday: 'short', day: 'numeric', month: 'short', year: 'numeric' };
    return date.toLocaleDateString('en-US', options);
}

function showLoadingState() {
    $('.containerEvents .oneEvent').html('<div class="text-center py-4">' +
        '<div class="spinner-border text-primary" role="status">' +
            '<span class="visually-hidden">Loading...</span>' +
        '</div>' +
        '<p class="mt-2">' + (label('common.loading') || 'Loading...') + '</p>' +
    '</div>');
}

function hideLoadingState() {
    // Loading state will be replaced by updateEventsList
}

function showErrorState() {
    $('.containerEvents .oneEvent').html('<div class="text-center py-4">' +
        '<i class="bi bi-exclamation-triangle text-warning fs-1"></i>' +
        '<p class="mt-2 text-muted">' + (label('common.error.loading') || 'Error loading events') + '</p>' +
        '<button class="btn btn-outline-primary btn-sm" onclick="applyFilters()">' +
            (label('common.retry') || 'Retry') + '</button>' +
    '</div>');
}

function updateUrlState(pageId, sortOrder) {
    // Update browser URL without page reload
    var url = new URL(window.location);
    var originalUrl = url.toString();

    if (pageId) {
        url.searchParams.set('pageId', pageId);
    } else {
        url.searchParams.delete('pageId');
    }

    if (sortOrder && sortOrder !== 'desc') {
        url.searchParams.set('sortOrder', sortOrder);
    } else {
        url.searchParams.delete('sortOrder');
    }

    var newUrl = url.toString();

    // Only update URL if it has actually changed
    if (originalUrl !== newUrl && history.pushState) {
        // Create state object for better history management
        var state = {
            pageId: pageId || null,
            sortOrder: sortOrder || 'desc',
            timestamp: Date.now()
        };

        history.pushState(state, null, newUrl);
    }
}

function checkUrlParameters() {
    // Check URL for pageId and sortOrder parameters
    var url = new URL(window.location);
    var pageIdParam = url.searchParams.get('pageId');
    var sortOrder = url.searchParams.get('sortOrder');

    if (pageIdParam) {
        // Handle both single pageId and comma-separated multiple pageIds
        var pageIds = pageIdParam.includes(',') ? pageIdParam.split(',') : [pageIdParam];

        // Filter out empty values
        pageIds = pageIds.filter(function(id) { return id && id.trim() !== ''; });

        if (pageIds.length > 0) {
            // Set values in Select2 multi-select dropdowns
            $('#pageFilter, #pageFilterHorizontal').val(pageIds).trigger('change.select2');
        }
    }

    if (sortOrder) {
        $('#sortOrder, #sortOrderHorizontal').val(sortOrder);
    }

    // Apply filters if parameters were found
    if (pageIdParam || sortOrder) {
        applyFilters();
    }
}

function showFilterFeedback(eventCount, pageId, sortOrder) {
    // Create a temporary feedback message
    var message = '';
    if (pageId) {
        // Handle multiple pageIds (comma-separated)
        var pageIds = pageId.split(',');
        var pageNames = [];

        pageIds.forEach(function(id) {
            var pageName = $('#pageFilter option[value="' + id + '"]').text() ||
                          $('#pageFilterHorizontal option[value="' + id + '"]').text();
            if (pageName) {
                pageNames.push(pageName);
            }
        });

        if (pageNames.length > 0) {
            message += (label('common.filtered.by.page') || 'Filtered by page') + ': ' + pageNames.join(', ');
        }
    }

    if (sortOrder && sortOrder !== 'desc') {
        if (message) message += ' | ';
        message += (label('common.sorted.by') || 'Sorted by') + ': ' +
                  (sortOrder === 'asc' ?
                   (label('common.oldest.first') || 'Oldest first') :
                   (label('common.newest.first') || 'Newest first'));
    }

    if (message) {
        message += ' (' + eventCount + ' ' + (label('common.events') || 'events') + ')';

        // Show temporary feedback
        var feedback = $('<div class="alert alert-info alert-dismissible fade show mt-2" role="alert">' +
            '<small>' + message + '</small>' +
            '<button type="button" class="btn-close btn-close-sm" data-bs-dismiss="alert"></button>' +
            '</div>');

        // Insert after filter controls
        $('.card-header').has('#pageFilter, #pageFilterHorizontal').after(feedback);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            feedback.fadeOut(function() {
                feedback.remove();
            });
        }, 5000);
    }
}

function setupBrowserNavigation() {
    // Handle browser back/forward navigation
    window.addEventListener('popstate', function(event) {
        // Re-read URL parameters and apply filters
        var url = new URL(window.location);
        var pageIdParam = url.searchParams.get('pageId');
        var sortOrder = url.searchParams.get('sortOrder');

        // Update dropdown values without triggering change events
        $('#pageFilter, #pageFilterHorizontal').off('change');
        $('#sortOrder, #sortOrderHorizontal').off('change');

        if (pageIdParam) {
            // Handle both single pageId and comma-separated multiple pageIds
            var pageIds = pageIdParam.includes(',') ? pageIdParam.split(',') : [pageIdParam];
            pageIds = pageIds.filter(function(id) { return id && id.trim() !== ''; });

            $('#pageFilter, #pageFilterHorizontal').val(pageIds).trigger('change.select2');
        } else {
            $('#pageFilter, #pageFilterHorizontal').val([]).trigger('change.select2');
        }

        if (sortOrder) {
            $('#sortOrder, #sortOrderHorizontal').val(sortOrder);
        } else {
            $('#sortOrder, #sortOrderHorizontal').val('desc');
        }

        // Re-bind events
        bindFilterEvents();

        // Apply filters based on URL state
        applyFiltersFromUrl(pageIdParam, sortOrder);
    });
}

function applyFiltersFromUrl(pageId, sortOrder) {
    // Apply filters without updating URL (to avoid infinite loop)
    showLoadingState();

    // Build URL for events endpoint
    var url = new URI($('#eventListUri').attr('href'));
    var eventId = getEventIdFromPage();
    if (eventId) {
        url.addSearch('eventId', eventId);
    }
    if (pageId) {
        url.addSearch('pageId', pageId);
    }
    if (sortOrder) {
        url.addSearch('sortOrder', sortOrder);
    }

    $.ajax({
        url: url.toString(),
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            hideLoadingState();

            if (response.success && response.events) {
                updateEventsList(response.events);
                // Don't update URL state here since we're responding to URL change

                // Show success feedback for filtered results
                if (pageId || (sortOrder && sortOrder !== 'desc')) {
                    showFilterFeedback(response.events.length, pageId, sortOrder);
                }
            } else {
                console.warn('Failed to load events:', response.error || 'Unknown error');
                showErrorState();
            }
        },
        error: function(xhr, status, error) {
            hideLoadingState();
            console.error('Error loading events:', error);
            showErrorState();
        }
    });
}

function initEventNavigation() {
    // Check if this event has a parent (is part of a series)
    var parentId = $('#eventParentId').text();
    var currentEventId = $('#eventId').text();

    if (!parentId || !currentEventId) {
        return; // No navigation needed
    }

    // Load navigation data
    loadEventNavigation(parentId, currentEventId);
}

function loadEventNavigation(parentId, currentEventId) {
    var url = new URI($('#eventNavigationUri').attr('href'));
    if (!url) {
        console.error('Missing navigation URL');
        return;
    }

    url.addSearch('parentId', parentId);
    url.addSearch('currentEventId', currentEventId);

    $.ajax({
        url: url.toString(),
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            var parsedResponse = JSON.parse(response);
            if (parsedResponse.success) {
                setupNavigationButtons(parsedResponse.previous, parsedResponse.next);
            } else {
                console.warn('Failed to load navigation:', parsedResponse.error || 'Unknown error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading navigation:', error);
        }
    });
}

function setupNavigationButtons(previousEvent, nextEvent) {
    var eventBaseUrl = $('#eventPathBaseUri').attr('href');

    // Setup previous button (both vertical and horizontal layouts)
    if (previousEvent) {
        var prevUrl = eventBaseUrl + '/' + previousEvent.identifier;
        $('#prevEventBtn, #prevEventBtnHorizontal').show().off('click').on('click', function(e) {
            e.preventDefault();
            window.location.href = prevUrl;
        });
    }

    // Setup next button (both vertical and horizontal layouts)
    if (nextEvent) {
        var nextUrl = eventBaseUrl + '/' + nextEvent.identifier;
        $('#nextEventBtn, #nextEventBtnHorizontal').show().off('click').on('click', function(e) {
            e.preventDefault();
            window.location.href = nextUrl;
        });
    }
}

function bindDisconnectChildren() {
    $('#disconnectChildrenBtn, #disconnectChildrenBtnHorizontal').off('click').on('click', function(event) {
        event.preventDefault();

        var eventId = $(this).data('event-id');
        if (!eventId) {
            console.error('missing eventId');
            return false;
        }

        var url = $('#eventDisconnectChildrenUri').attr('href');
        if (!url) {
            console.error('missing disconnect URL');
            return false;
        }

        // Show confirmation dialog
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="bi bi-exclamation-triangle icon-2x"></i><br/><br/> ' + label('event.disconnect.children.confirm.title'),
            content: label('event.disconnect.children.confirm.message'),
            columnClass: "col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                cancel: {
                    text: label('common.cancel'),
                    btnClass: 'btn-default',
                    action: function () {
                        // Do nothing, just close
                    }
                },
                confirm: {
                    text: label('common.confirm'),
                    btnClass: 'btn-danger',
                    action: function () {
                        // Perform the disconnect operation
                        $.blockUI();
                        $.ajax({
                            url: url,
                            type: 'POST',
                            data: { oid: eventId },
                            success: function(response) {
                                $.unblockUI();
                                // Reload the page to show updated state
                                location.reload();
                            },
                            error: function(xhr, status, error) {
                                $.unblockUI();
                                console.error('Error disconnecting children:', error);
                                alert('Errore durante lo scollegamento degli eventi. Riprova più tardi.');
                            }
                        });
                    }
                }
            }
        });
    });
}

function showFollowPagesModal(pages) {
    // Clear previous content
    $('#followPagesContainer').empty();

    if (!pages || pages.length === 0) {
        return;
    }

    // Build pages list
    var pagesHtml = '';
    pages.forEach(function(page) {
        var isFollowing = page.isFollowing || false;
        var buttonClass = isFollowing ? 'btn-outline-danger' : 'btn-outline-primary';
        var buttonIcon = isFollowing ? 'bi-dash-circle-fill' : 'bi-plus-circle-fill';
        var buttonText = isFollowing ? label('common.unfollow') : label('common.follow');
        var buttonAction = isFollowing ? 'unfollow' : 'follow';

        // Build image URL if available
        var imageHtml = '';
        if (page.profileImageId) {
            var imageUrl = $('#imageUri').attr('href') + '?oid=' + page.profileImageId;
            imageHtml = '<img src="' + imageUrl + '" alt="' + page.name + '" class="rounded-circle me-3" width="50" height="50">';
        } else {
            imageHtml = '<div class="bg-light rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;"><i class="bi bi-person-fill text-muted"></i></div>';
        }

        var url = $("#pageDetailUri").attr("href").replace(":identifier", page.identifier);
        pagesHtml += '<div class="d-flex align-items-center justify-content-between p-3 border rounded mb-3" data-page-id="' + page.id + '">' +
            '<div class="d-flex align-items-center">' +
                imageHtml +
                '<div>' +
                    '<h6 class="mb-0"><a href="' + url + '" target="_blank">' + page.name + '</a></h6>' +
                    (page.shortDescription ? '<p class="mb-0 small">' + page.shortDescription + '</p>' : '') +
                '</div>' +
            '</div>' +
            '<button class="btn ' + buttonClass + ' btn-sm page-follow-toggle" ' +
                'data-page-id="' + page.id + '" ' +
                'data-action="' + buttonAction + '" ' +
                'data-following="' + isFollowing + '">' +
                '<i class="' + buttonIcon + ' me-1"></i>' +
                buttonText +
            '</button>' +
        '</div>';
    });

    $('#followPagesContainer').html(pagesHtml);

    // Bind click events for individual page follow buttons
    bindPageFollowToggle();

    // Show the modal
    var modal = new bootstrap.Modal(document.getElementById('followPagesModal'));
    modal.show();
}

function bindPageFollowToggle() {
    $('.page-follow-toggle').off('click').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var pageId = button.data('page-id');
        var action = button.data('action');
        var isCurrentlyFollowing = button.data('following');

        // Toggle the button state immediately for better UX
        togglePageFollowButton(button, !isCurrentlyFollowing);

        // Make AJAX call to toggle follow status
        var url = new URI($('#pageFollowToggleUri').attr('href'));
        url.addSearch('pageId', pageId);

        $.ajax({
            url: url.toString(),
            type: 'POST',
            // dataType: 'json',
            success: function(response) {
                // Button state already updated, just update data attribute
                button.data('following', !isCurrentlyFollowing);
                button.data('action', !isCurrentlyFollowing ? 'unfollow' : 'follow');
            },
            error: function(xhr, status, error) {
                // Revert button state on error
                togglePageFollowButton(button, isCurrentlyFollowing);
                console.error('Error toggling page follow:', error);
            }
        });
    });
}

function togglePageFollowButton(button, isFollowing) {
    if (isFollowing) {
        button.removeClass('btn-outline-primary').addClass('btn-outline-danger');
        button.find('i').removeClass('bi-plus-circle-fill').addClass('bi-dash-circle-fill');
        button.contents().filter(function() { return this.nodeType === 3; }).last().replaceWith(label('common.unfollow'));
        button.data('action', 'unfollow');
    } else {
        button.removeClass('btn-outline-danger').addClass('btn-outline-primary');
        button.find('i').removeClass('bi-dash-circle-fill').addClass('bi-plus-circle-fill');
        button.contents().filter(function() { return this.nodeType === 3; }).last().replaceWith(label('common.follow'));
        button.data('action', 'follow');
    }
    button.data('following', isFollowing);
}