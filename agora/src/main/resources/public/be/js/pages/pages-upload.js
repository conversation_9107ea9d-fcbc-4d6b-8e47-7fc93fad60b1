$(document).ready(function () {

    registerUpload();

});

// Global variables for status polling
var statusPollingInterval = null;
var currentUploadId = null;

function registerUpload() {

    // registering upload submit action
    $("form#form-upload-pages-verify").off("submit");
    $("form#form-upload-pages-verify").submit(function (event) {
        event.preventDefault();

        var formData = new FormData($(this)[0]);
        var url = $(this).attr('action');
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {

                        var uploadId = returndata;
                        $.unblockUI();
                        // refresh panel
                        var url = new URI();
                        url.removeSearch("uploadId");
                        url.addSearch("uploadId", uploadId);

                        $("#upload-page-panel").load(url + " #upload-page-panel", function () {
                            $(document).ready(function () {

                                // re-registering upload submit action
                                registerUpload();

                            });
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();
                        // error
                        console.log("response: " + JSON.stringify(response));
                        console.log("status: " + status);
                        console.log("errorThrown: " + errorThrown);
                    }
        });

        return false;
    });

    // registering save submit action with progress tracking
    $("form#form-upload-pages-save").off("submit");
    $("form#form-upload-pages-save").submit(function (event) {
        event.preventDefault();

        var url = $(this).attr('action');
        var urlParams = new URLSearchParams(url.split('?')[1]);
        currentUploadId = urlParams.get('uploadId');

        if (currentUploadId) {
            // Show progress container
            $("#upload-progress-container").show();
            $("#upload-errors-container").hide();

            // Start status polling
            startStatusPolling();

            // Submit the form
            $.ajax({
                url: url,
                type: 'POST',
                success: function(response) {
                    // The redirect will be handled by the server
                    // Status polling will continue until completion
                },
                error: function(response, status, errorThrown) {
                    stopStatusPolling();
                    console.log("Error submitting form: " + errorThrown);
                    $("#upload-progress-container").hide();
                }
            });
        }

        return false;
    });

}

/**
 * Start polling for upload status
 */
function startStatusPolling() {
    if (statusPollingInterval) {
        clearInterval(statusPollingInterval);
    }

    statusPollingInterval = setInterval(function() {
        if (currentUploadId) {
            checkUploadStatus();
        }
    }, 2000); // Poll every 2 seconds

    // Initial status check
    checkUploadStatus();
}

/**
 * Stop polling for upload status
 */
function stopStatusPolling() {
    if (statusPollingInterval) {
        clearInterval(statusPollingInterval);
        statusPollingInterval = null;
    }
}

/**
 * Check upload status via AJAX
 */
function checkUploadStatus() {
    if (!currentUploadId) return;

    var url = $("#pagesUploadStatusUri").attr("href");
    $.ajax({
        url: url + '?uploadId=' + currentUploadId,
        type: 'GET',
        dataType: 'json',
        success: function(status) {
            updateProgressDisplay(status);

            // Check if upload is completed or has errors
            if (status.status === 'COMPLETED' || status.status === 'ERROR') {
                stopStatusPolling();

                if (status.status === 'COMPLETED') {
                    // Redirect to success page after a short delay
                    /*setTimeout(function() {
                        window.location.href = window.location.pathname + '/ok';
                    }, 2000);*/
                } else if (status.status === 'ERROR') {
                    // Show error state
                    $("#upload-progress-bar").removeClass('progress-bar-info').addClass('progress-bar-danger');
                    $("#upload-current-operation").text('Errore durante l\'elaborazione');
                }
            }
        },
        error: function(xhr, status, error) {
            console.log('Error checking upload status:', error);
            // Continue polling even on error, might be temporary
        }
    });
}

/**
 * Update progress display based on status
 */
function updateProgressDisplay(status) {
    var percentage = Math.round(status.progressPercentage || 0);
    var processedRows = status.processedRows || 0;
    var totalRows = status.totalRows || 0;
    var currentOperation = status.currentOperation || 'In elaborazione...';

    // Update progress bar
    $("#upload-progress-bar").css('width', percentage + '%');
    $("#upload-progress-text").text(percentage + '%');

    // Update counters
    $("#upload-processed-count").text(processedRows);
    $("#upload-total-count").text(totalRows);
    $("#upload-current-operation").text(currentOperation);

    // Update progress bar color based on status
    var progressBar = $("#upload-progress-bar");
    progressBar.removeClass('progress-bar-info progress-bar-success progress-bar-danger');

    switch(status.status) {
        case 'PROCESSING':
            progressBar.addClass('progress-bar-info');
            break;
        case 'COMPLETED':
            progressBar.addClass('progress-bar-success');
            break;
        case 'ERROR':
            progressBar.addClass('progress-bar-danger');
            break;
        default:
            progressBar.addClass('progress-bar-info');
    }

    // Show errors if any
    if (status.errors && status.errors.length > 0) {
        displayErrors(status.errors);
    }
}

/**
 * Display errors in the error table
 */
function displayErrors(errors) {
    var errorTableBody = $("#upload-errors-table");
    errorTableBody.empty();

    errors.forEach(function(error) {
        var row = $('<tr>');
        row.append($('<td>').text(error.rowNumber));
        row.append($('<td>').text(error.errorMessage));
        row.append($('<td>').html('<small>' + (error.rowData || 'N/A') + '</small>'));
        errorTableBody.append(row);
    });

    // Update error count badge
    $("#error-count-badge").text(errors.length);

    $("#upload-errors-container").show();
}

